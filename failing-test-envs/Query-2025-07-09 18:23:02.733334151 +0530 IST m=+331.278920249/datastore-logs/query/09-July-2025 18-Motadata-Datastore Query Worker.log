09-July-2025 06:17:31.829144 PM INFO [Query Worker]:query 0 started with pool length 20000
09-July-2025 06:17:31.831305 PM INFO [Query Worker]:query 1 started with pool length 20000
09-July-2025 06:17:31.833225 PM INFO [Query Worker]:query 2 started with pool length 20000
09-July-2025 06:17:31.835311 PM INFO [Query Worker]:query 3 started with pool length 20000
09-July-2025 06:17:31.837345 PM INFO [Query Worker]:query 4 started with pool length 20000
09-July-2025 06:17:31.839245 PM INFO [Query Worker]:query 5 started with pool length 20000
09-July-2025 06:17:31.841168 PM INFO [Query Worker]:query 6 started with pool length 20000
09-July-2025 06:17:31.843206 PM INFO [Query Worker]:query 7 started with pool length 20000
09-July-2025 06:17:31.845080 PM INFO [Query Worker]:query 8 started with pool length 20000
09-July-2025 06:17:31.847083 PM INFO [Query Worker]:query 9 started with pool length 20000
09-July-2025 06:17:31.848944 PM INFO [Query Worker]:query 10 started with pool length 10000
09-July-2025 06:17:31.850700 PM INFO [Query Worker]:query 11 started with pool length 10000
09-July-2025 06:17:31.852571 PM INFO [Query Worker]:query 12 started with pool length 10000
09-July-2025 06:17:31.854404 PM INFO [Query Worker]:query 13 started with pool length 10000
09-July-2025 06:17:31.856197 PM INFO [Query Worker]:query 14 started with pool length 10000
09-July-2025 06:17:31.858162 PM INFO [Query Worker]:query 15 started with pool length 10000
09-July-2025 06:17:31.860038 PM INFO [Query Worker]:query 16 started with pool length 10000
09-July-2025 06:17:31.861939 PM INFO [Query Worker]:query 17 started with pool length 10000
09-July-2025 06:17:31.863913 PM INFO [Query Worker]:query 18 started with pool length 10000
09-July-2025 06:17:31.865880 PM INFO [Query Worker]:query 19 started with pool length 10000
09-July-2025 06:17:31.867732 PM INFO [Query Worker]:query 20 started with pool length 10000
09-July-2025 06:17:31.869541 PM INFO [Query Worker]:query 21 started with pool length 10000
09-July-2025 06:17:31.871354 PM INFO [Query Worker]:query 22 started with pool length 10000
09-July-2025 06:17:31.873226 PM INFO [Query Worker]:query 23 started with pool length 10000
09-July-2025 06:17:31.875013 PM INFO [Query Worker]:query 24 started with pool length 10000
09-July-2025 06:17:31.876858 PM INFO [Query Worker]:query 25 started with pool length 10000
09-July-2025 06:17:31.878888 PM INFO [Query Worker]:query 26 started with pool length 10000
09-July-2025 06:17:31.880999 PM INFO [Query Worker]:query 27 started with pool length 10000
09-July-2025 06:17:31.883096 PM INFO [Query Worker]:query 28 started with pool length 10000
09-July-2025 06:17:31.885268 PM INFO [Query Worker]:query 29 started with pool length 10000
09-July-2025 06:17:31.887446 PM INFO [Query Worker]:query 30 started with pool length 10000
09-July-2025 06:17:31.889536 PM INFO [Query Worker]:query 31 started with pool length 10000
09-July-2025 06:17:31.891475 PM INFO [Query Worker]:query 32 started with pool length 10000
09-July-2025 06:17:31.893733 PM INFO [Query Worker]:query 33 started with pool length 10000
09-July-2025 06:17:31.896005 PM INFO [Query Worker]:query 34 started with pool length 10000
09-July-2025 06:17:31.898939 PM INFO [Query Worker]:query 35 started with pool length 10000
09-July-2025 06:17:31.901098 PM INFO [Query Worker]:query 36 started with pool length 10000
09-July-2025 06:17:31.903525 PM INFO [Query Worker]:query 37 started with pool length 10000
09-July-2025 06:17:31.905683 PM INFO [Query Worker]:query 38 started with pool length 10000
09-July-2025 06:17:31.907929 PM INFO [Query Worker]:query 39 started with pool length 10000
09-July-2025 06:17:31.910670 PM INFO [Query Worker]:query 40 started with pool length 20000
09-July-2025 06:17:31.913055 PM INFO [Query Worker]:query 41 started with pool length 20000
09-July-2025 06:17:31.915066 PM INFO [Query Worker]:query 42 started with pool length 20000
09-July-2025 06:17:31.917127 PM INFO [Query Worker]:query 43 started with pool length 20000
09-July-2025 06:17:31.919404 PM INFO [Query Worker]:query 44 started with pool length 20000
09-July-2025 06:17:31.921854 PM INFO [Query Worker]:query 45 started with pool length 20000
09-July-2025 06:17:31.924197 PM INFO [Query Worker]:query 46 started with pool length 20000
09-July-2025 06:17:31.926314 PM INFO [Query Worker]:query 47 started with pool length 20000
09-July-2025 06:17:31.928596 PM INFO [Query Worker]:query 48 started with pool length 20000
09-July-2025 06:17:31.930900 PM INFO [Query Worker]:query 49 started with pool length 20000
09-July-2025 06:17:33.192554 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 0
09-July-2025 06:17:33.192669 PM DEBUG [Query Worker]:worker event 0 of worker 0 got keys 4 (executor 0)
09-July-2025 06:17:33.192736 PM DEBUG [Query Worker]:worker event 0 of worker 0 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.193044 PM DEBUG [Query Worker]:worker event 0 of worker 0 fired 4 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.193130 PM TRACE [Query Worker]:worker event 0 of worker 0 set group 12 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.193193 PM TRACE [Query Worker]:worker 0 for metric 12^3543545^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.193427 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 4
09-July-2025 06:17:33.193469 PM TRACE [Query Worker]:worker 0 for metric 12^3543545^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.193502 PM DEBUG [Query Worker]:worker event 0 of worker 4 got keys 4 (executor 0)
09-July-2025 06:17:33.193533 PM TRACE [Query Worker]:worker 0 for tick 12^3543545^slo~status^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.193557 PM DEBUG [Query Worker]:worker event 0 of worker 4 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.193592 PM TRACE [Query Worker]:worker 0 for tick 12^3543545^slo~status^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.193762 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 5
09-July-2025 06:17:33.193841 PM DEBUG [Query Worker]:worker event 0 of worker 5 got keys 6 (executor 0)
09-July-2025 06:17:33.193846 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 7
09-July-2025 06:17:33.193897 PM DEBUG [Query Worker]:worker event 0 of worker 5 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.193909 PM TRACE [Query Worker]:worker event 0 of worker 0 set group 13 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.193940 PM DEBUG [Query Worker]:worker event 0 of worker 7 got keys 6 (executor 0)
09-July-2025 06:17:33.193983 PM TRACE [Query Worker]:worker 0 for metric 13^3545645^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.194005 PM DEBUG [Query Worker]:worker event 0 of worker 7 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.194054 PM TRACE [Query Worker]:worker 0 for metric 13^3545645^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.194111 PM TRACE [Query Worker]:worker 0 for tick 13^3545645^slo~status^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.194112 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 6
09-July-2025 06:17:33.194172 PM TRACE [Query Worker]:worker 0 for tick 13^3545645^slo~status^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.194197 PM DEBUG [Query Worker]:worker event 0 of worker 6 got keys 4 (executor 0)
09-July-2025 06:17:33.194256 PM DEBUG [Query Worker]:worker event 0 of worker 6 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.194404 PM DEBUG [Query Worker]:worker event 0 of worker 7 fired 6 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.194467 PM TRACE [Query Worker]:worker event 0 of worker 7 set group 14 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.194505 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 2
09-July-2025 06:17:33.194538 PM TRACE [Query Worker]:worker 7 for metric 14^879785^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.194593 PM DEBUG [Query Worker]:worker event 0 of worker 2 got keys 4 (executor 0)
09-July-2025 06:17:33.194651 PM DEBUG [Query Worker]:worker event 0 of worker 2 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.194687 PM TRACE [Query Worker]:worker 7 for metric 14^879785^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.194741 PM TRACE [Query Worker]:worker 7 for tick 14^879785^slo~error.budget.left^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.194801 PM TRACE [Query Worker]:worker 7 for tick 14^879785^slo~error.budget.left^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.194888 PM DEBUG [Query Worker]:worker event 0 of worker 4 fired 4 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.194943 PM TRACE [Query Worker]:worker event 0 of worker 4 set group 10 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.194987 PM TRACE [Query Worker]:worker 4 for metric 10^12312^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195105 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 1
09-July-2025 06:17:33.195133 PM TRACE [Query Worker]:worker event 0 of worker 7 set group 10 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.195189 PM TRACE [Query Worker]:worker 7 for metric 10^12312^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195211 PM DEBUG [Query Worker]:worker event 0 of worker 1 got keys 6 (executor 0)
09-July-2025 06:17:33.195221 PM TRACE [Query Worker]:worker 4 for metric 10^12312^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195253 PM TRACE [Query Worker]:worker 7 for metric 10^12312^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195277 PM TRACE [Query Worker]:worker 4 for tick 10^12312^slo~violated.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.195287 PM DEBUG [Query Worker]:worker event 0 of worker 1 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.195322 PM TRACE [Query Worker]:worker 7 for tick 10^12312^slo~error.budget.left^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.195344 PM TRACE [Query Worker]:worker 4 for tick 10^12312^slo~violated.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.195377 PM TRACE [Query Worker]:worker 7 for tick 10^12312^slo~error.budget.left^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.195464 PM TRACE [Query Worker]:worker event 0 of worker 7 set group 11 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.195516 PM TRACE [Query Worker]:worker 7 for metric 11^231212^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195571 PM TRACE [Query Worker]:worker 7 for metric 11^231212^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195620 PM TRACE [Query Worker]:worker 7 for tick 11^231212^slo~error.budget.left^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.195669 PM TRACE [Query Worker]:worker 7 for tick 11^231212^slo~error.budget.left^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.195695 PM TRACE [Query Worker]:worker event 0 of worker 4 set group 11 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.195751 PM TRACE [Query Worker]:worker 4 for metric 11^231212^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195773 PM DEBUG [Query Worker]:request 5314072803671^0^0 received on query worker 3
09-July-2025 06:17:33.195803 PM TRACE [Query Worker]:worker 4 for metric 11^231212^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.195851 PM DEBUG [Query Worker]:worker event 0 of worker 3 got keys 6 (executor 0)
09-July-2025 06:17:33.195857 PM TRACE [Query Worker]:worker 4 for tick 11^231212^slo~violated.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.195905 PM TRACE [Query Worker]:worker 4 for tick 11^231212^slo~violated.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.195913 PM DEBUG [Query Worker]:worker event 0 of worker 3 got groups 1 ([monitor         ]) (executor 0)
09-July-2025 06:17:33.196121 PM DEBUG [Query Worker]:worker event 0 of worker 5 fired 6 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.196193 PM TRACE [Query Worker]:worker event 0 of worker 5 set group 12 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.196237 PM TRACE [Query Worker]:worker 5 for metric 12^3543545^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.196520 PM DEBUG [Query Worker]:worker event 0 of worker 6 fired 4 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.196596 PM TRACE [Query Worker]:worker 5 for metric 12^3543545^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.196611 PM TRACE [Query Worker]:worker event 0 of worker 6 set group 12 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.196654 PM TRACE [Query Worker]:worker 5 for tick 12^3543545^slo~violated.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.196673 PM TRACE [Query Worker]:worker 6 for metric 12^3543545^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.196701 PM TRACE [Query Worker]:worker 5 for tick 12^3543545^slo~violated.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.196903 PM TRACE [Query Worker]:worker 6 for metric 12^3543545^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.196963 PM TRACE [Query Worker]:worker 6 for tick 12^3543545^slo~error.budget.left^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.197012 PM TRACE [Query Worker]:worker event 0 of worker 5 set group 13 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.197031 PM TRACE [Query Worker]:worker 6 for tick 12^3543545^slo~error.budget.left^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.197070 PM TRACE [Query Worker]:worker 5 for metric 13^3545645^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197122 PM TRACE [Query Worker]:worker 5 for metric 13^3545645^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197178 PM TRACE [Query Worker]:worker 5 for tick 13^3545645^slo~violated.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.197225 PM TRACE [Query Worker]:worker 5 for tick 13^3545645^slo~violated.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.197358 PM TRACE [Query Worker]:worker event 0 of worker 6 set group 13 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.197424 PM TRACE [Query Worker]:worker 6 for metric 13^3545645^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197491 PM TRACE [Query Worker]:worker 6 for metric 13^3545645^slo~error.budget.left^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197533 PM TRACE [Query Worker]:worker event 0 of worker 5 set group 14 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.197547 PM TRACE [Query Worker]:worker 6 for tick 13^3545645^slo~error.budget.left^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.197588 PM TRACE [Query Worker]:worker 5 for metric 14^879785^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197580 PM DEBUG [Query Worker]:worker event 0 of worker 1 fired 6 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.197605 PM TRACE [Query Worker]:worker 6 for tick 13^3545645^slo~error.budget.left^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.197640 PM TRACE [Query Worker]:worker 5 for metric 14^879785^slo~violated.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197650 PM TRACE [Query Worker]:worker event 0 of worker 1 set group 14 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.197687 PM TRACE [Query Worker]:worker 5 for tick 14^879785^slo~violated.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.197732 PM TRACE [Query Worker]:worker 5 for tick 14^879785^slo~violated.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.197718 PM TRACE [Query Worker]:worker 1 for metric 14^879785^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197917 PM TRACE [Query Worker]:worker 1 for metric 14^879785^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.197971 PM TRACE [Query Worker]:worker 1 for tick 14^879785^slo~status^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.198030 PM TRACE [Query Worker]:worker 1 for tick 14^879785^slo~status^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.196267 PM DEBUG [Query Worker]:worker event 0 of worker 3 fired 6 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.198134 PM TRACE [Query Worker]:worker event 0 of worker 3 set group 14 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.198189 PM TRACE [Query Worker]:worker 3 for metric 14^879785^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198278 PM TRACE [Query Worker]:worker 3 for metric 14^879785^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198345 PM TRACE [Query Worker]:worker 3 for tick 14^879785^slo~achieved.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.198400 PM TRACE [Query Worker]:worker event 0 of worker 1 set group 10 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.198397 PM TRACE [Query Worker]:worker 3 for tick 14^879785^slo~achieved.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.198454 PM TRACE [Query Worker]:worker 1 for metric 10^12312^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198515 PM TRACE [Query Worker]:worker 1 for metric 10^12312^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198564 PM TRACE [Query Worker]:worker 1 for tick 10^12312^slo~status^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.198612 PM TRACE [Query Worker]:worker 1 for tick 10^12312^slo~status^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.198673 PM TRACE [Query Worker]:worker event 0 of worker 1 set group 11 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.198704 PM TRACE [Query Worker]:worker event 0 of worker 3 set group 10 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.198727 PM TRACE [Query Worker]:worker 1 for metric 11^231212^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198761 PM TRACE [Query Worker]:worker 3 for metric 10^12312^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198784 PM TRACE [Query Worker]:worker 1 for metric 11^231212^slo~status^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198833 PM TRACE [Query Worker]:worker 3 for metric 10^12312^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.198851 PM TRACE [Query Worker]:worker 1 for tick 11^231212^slo~status^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.198889 PM TRACE [Query Worker]:worker 3 for tick 10^12312^slo~achieved.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.198910 PM TRACE [Query Worker]:worker 1 for tick 11^231212^slo~status^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.198953 PM TRACE [Query Worker]:worker 3 for tick 10^12312^slo~achieved.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.199029 PM TRACE [Query Worker]:worker event 0 of worker 3 set group 11 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.199097 PM TRACE [Query Worker]:worker 3 for metric 11^231212^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.199158 PM TRACE [Query Worker]:worker 3 for metric 11^231212^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.199212 PM TRACE [Query Worker]:worker 3 for tick 11^231212^slo~achieved.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.200080 PM TRACE [Query Worker]:worker 3 for tick 11^231212^slo~achieved.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.201238 PM DEBUG [Query Worker]:worker event 0 of worker 2 fired 4 i/o requests (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.201354 PM TRACE [Query Worker]:worker event 0 of worker 2 set group 12 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.201479 PM TRACE [Query Worker]:worker 2 for metric 12^3543545^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.201714 PM TRACE [Query Worker]:worker 2 for metric 12^3543545^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.201775 PM TRACE [Query Worker]:worker 2 for tick 12^3543545^slo~achieved.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.201823 PM TRACE [Query Worker]:worker 2 for tick 12^3543545^slo~achieved.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.202142 PM TRACE [Query Worker]:worker event 0 of worker 2 set group 13 (executor 0) (09072025-0-500031-slo.metric)
09-July-2025 06:17:33.202196 PM TRACE [Query Worker]:worker 2 for metric 13^3545645^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.202252 PM TRACE [Query Worker]:worker 2 for metric 13^3545645^slo~achieved.percentage^0 i/o request done (executor 0) 
09-July-2025 06:17:33.202304 PM TRACE [Query Worker]:worker 2 for tick 13^3545645^slo~achieved.percentage^0 decoding done with size (96) (executor 0) 
09-July-2025 06:17:33.202357 PM TRACE [Query Worker]:worker 2 for tick 13^3545645^slo~achieved.percentage^time^0 filtering done for totime->fromtime (174055620->174228420) : size (52) (executor 0) 
09-July-2025 06:17:33.210940 PM DEBUG [Query Worker]:Cleanup routine called of worker 0 by executor 0
09-July-2025 06:17:33.211016 PM DEBUG [Query Worker]:groups size 2 of worker 0 (executor 0)
09-July-2025 06:17:33.211235 PM DEBUG [Query Worker]:worker pool 0 (data type -> 112) of column was released slo~status^last (executor 0)
09-July-2025 06:17:33.211411 PM DEBUG [Query Worker]:Cleanup routine called of worker 1 by executor 0
09-July-2025 06:17:33.211485 PM DEBUG [Query Worker]:groups size 3 of worker 1 (executor 0)
09-July-2025 06:17:33.211991 PM DEBUG [Query Worker]:worker pool 0 (data type -> 112) of column was released slo~status^last (executor 0)
09-July-2025 06:17:33.212140 PM DEBUG [Query Worker]:Cleanup routine called of worker 2 by executor 0
09-July-2025 06:17:33.212200 PM DEBUG [Query Worker]:groups size 2 of worker 2 (executor 0)
09-July-2025 06:17:33.212868 PM DEBUG [Query Worker]:worker pool 1 (data type -> 160) of column was released slo~achieved.percentage^last (executor 0)
09-July-2025 06:17:33.213073 PM DEBUG [Query Worker]:Cleanup routine called of worker 3 by executor 0
09-July-2025 06:17:33.213132 PM DEBUG [Query Worker]:groups size 3 of worker 3 (executor 0)
09-July-2025 06:17:33.213638 PM DEBUG [Query Worker]:worker pool 1 (data type -> 160) of column was released slo~achieved.percentage^last (executor 0)
09-July-2025 06:17:33.213805 PM DEBUG [Query Worker]:Cleanup routine called of worker 4 by executor 0
09-July-2025 06:17:33.213863 PM DEBUG [Query Worker]:groups size 2 of worker 4 (executor 0)
09-July-2025 06:17:33.214362 PM DEBUG [Query Worker]:worker pool 1 (data type -> 160) of column was released slo~violated.percentage^last (executor 0)
09-July-2025 06:17:33.214514 PM DEBUG [Query Worker]:Cleanup routine called of worker 5 by executor 0
09-July-2025 06:17:33.214584 PM DEBUG [Query Worker]:groups size 3 of worker 5 (executor 0)
09-July-2025 06:17:33.215083 PM DEBUG [Query Worker]:worker pool 1 (data type -> 160) of column was released slo~violated.percentage^last (executor 0)
09-July-2025 06:17:33.215232 PM DEBUG [Query Worker]:Cleanup routine called of worker 6 by executor 0
09-July-2025 06:17:33.215300 PM DEBUG [Query Worker]:groups size 2 of worker 6 (executor 0)
09-July-2025 06:17:33.215788 PM DEBUG [Query Worker]:worker pool 1 (data type -> 160) of column was released slo~error.budget.left^last (executor 0)
09-July-2025 06:17:33.215967 PM DEBUG [Query Worker]:Cleanup routine called of worker 7 by executor 0
09-July-2025 06:17:33.216025 PM DEBUG [Query Worker]:groups size 3 of worker 7 (executor 0)
09-July-2025 06:17:33.216536 PM DEBUG [Query Worker]:worker pool 1 (data type -> 160) of column was released slo~error.budget.left^last (executor 0)
