{"blob.columns": {"config.operation.output": {}, "event": {}, "message": {}, "runbook.worklog.error": {}, "runbook.worklog.result": {}, "trap.message": {}}, "floating.columns": {"dummy.numeric.column": {}, "dummy.numeric.column2": {}, "dummy.numeric.column3": {}, "interface~bandwidth": {}, "netroute.max.ping.latency": {}, "netroute.min.ping.latency": {}, "netroute.ping.latency": {}, "slo.instance~achieved.percentage": {}, "slo.instance~burn.rate": {}, "slo.instance~error.budget.left": {}, "slo.instance~violated.percentage": {}, "slo~achieved.percentage": {}, "slo~burn.rate": {}, "slo~error.budget.left": {}, "slo~violated.percentage": {}, "system.cpu.percent": {}}, "indexable.columns": {"21001-audit": {"audit.module": {}, "audit.operation": {}, "audit.remote.ip": {}, "audit.status": {}, "audit.user": {}, "event.source": {}}, "21002-notification": {"event.source": {}, "user.notification.severity": {}, "user.notification.type": {}}, "21003-flow": {"application": {}, "application.protocol": {}, "destination.as": {}, "destination.asn": {}, "destination.city": {}, "destination.country": {}, "destination.domain": {}, "destination.if.index": {}, "destination.ip": {}, "destination.isp": {}, "destination.port": {}, "destination.threat": {}, "event.source": {}, "peer.destination": {}, "peer.source": {}, "protocol": {}, "source.as": {}, "source.asn": {}, "source.city": {}, "source.country": {}, "source.domain": {}, "source.if.index": {}, "source.ip": {}, "source.isp": {}, "source.port": {}, "source.threat": {}, "tcp.flags": {}, "tos": {}}, "21005-flap.duration": {"instance": {}, "object.id": {}}, "21006-linux.logout.audit": {"event.category": {}, "event.pattern.id": {}, "event.source": {}, "event.source.type": {}, "level.description": {}, "log.id": {}, "log.in.count": {}, "log.message": {}, "source.port": {}, "system.os": {}, "uag.id": {}}, "21007-event.policy": {"event.source": {}, "metric": {}, "severity": {}}, "21008-metric.policy": {"metric": {}, "object.id": {}, "policy.type": {}, "severity": {}}, "21009-apache.login.audit": {"event.category": {}, "event.pattern.id": {}, "event.source": {}, "event.source.type": {}, "level.description": {}, "log.id": {}, "log.in.count": {}, "log.message": {}, "source.port": {}, "system.os": {}, "uag.id": {}}, "21010-flow": {"application": {}, "application.protocol": {}, "destination.as": {}, "destination.asn": {}, "destination.city": {}, "destination.country": {}, "destination.domain": {}, "destination.if.index": {}, "destination.ip": {}, "destination.isp": {}, "destination.port": {}, "destination.threat": {}, "event.source": {}, "peer.destination": {}, "peer.source": {}, "protocol": {}, "source.as": {}, "source.asn": {}, "source.city": {}, "source.country": {}, "source.domain": {}, "source.if.index": {}, "source.ip": {}, "source.isp": {}, "source.port": {}, "source.threat": {}, "tcp.flags": {}, "tos": {}}, "21011-trap": {"event.source": {}, "trap.enterprise.id": {}, "trap.name": {}, "trap.oid": {}, "trap.severity": {}, "trap.vendor": {}, "trap.version": {}}, "21012-audit": {"audit.module": {}, "audit.operation": {}, "audit.remote.ip": {}, "audit.status": {}, "audit.user": {}, "event.source": {}}, "499998-policy.flap": {"instance": {}, "object.id": {}, "policy.id": {}, "severity": {}}, "499999-event.history": {"event.category": {}, "event.pattern.id": {}, "event.source": {}, "event.source.type": {}}, "500000-flow": {"application": {}, "application.protocol": {}, "destination.as": {}, "destination.asn": {}, "destination.city": {}, "destination.country": {}, "destination.domain": {}, "destination.if.index": {}, "destination.ip": {}, "destination.isp": {}, "destination.port": {}, "destination.threat": {}, "event.source": {}, "peer.destination": {}, "peer.source": {}, "protocol": {}, "source.as": {}, "source.asn": {}, "source.city": {}, "source.country": {}, "source.domain": {}, "source.if.index": {}, "source.ip": {}, "source.isp": {}, "source.port": {}, "source.threat": {}, "tcp.flags": {}, "tos": {}}, "500003-trap": {"event.source": {}, "trap.enterprise.id": {}, "trap.name": {}, "trap.oid": {}, "trap.severity": {}, "trap.vendor": {}, "trap.version": {}}, "500014-health.metric": {"engine.type": {}, "event.source": {}}, "500018-config": {"event.source": {}}, "500022-runbook.worklog": {"event.source": {}, "object.id": {}, "policy.id": {}, "runbook.worklog.id": {}, "runbook.worklog.type": {}}, "50909-windows.login.audit": {"event.category": {}, "event.pattern.id": {}, "event.source": {}, "event.source.type": {}, "level.description": {}, "log.id": {}, "log.in.count": {}, "log.message": {}, "source.port": {}, "system.os": {}, "uag.id": {}}, "51919-windows.logout.audit": {"event.category": {}, "event.pattern.id": {}, "event.source": {}, "event.source.type": {}, "level.description": {}, "log.id": {}, "log.in.count": {}, "log.message": {}, "source.port": {}, "system.os": {}, "uag.id": {}}, "679760-oracle.login": {"event.category": {}, "event.pattern.id": {}, "event.source": {}, "event.source.type": {}, "level.description": {}, "log.id": {}, "log.in.count": {}, "log.message": {}, "source.port": {}, "system.os": {}, "uag.id": {}}}, "searchable.columns": {"interface": {}, "interface~admin.status": {}, "interface~last.change": {}, "interface~operational.status": {}, "interface~status": {}, "message": {}}}