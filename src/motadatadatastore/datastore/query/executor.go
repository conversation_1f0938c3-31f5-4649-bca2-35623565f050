/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
* 2025-05-14             Vedant Dokania         Motadata -6249  Records length changes
* 2025-06-03			 Dhaval <PERSON>-6393  Updated With Master Branch
* 2025-06-24			 Dhaval <PERSON>ra			<PERSON>-6639  Added Retention Check In Query Parsing
* 2025-06-23             Vedant Dokania         Motadata-6370 Mapping operand changes to get the instance type store
* 2025-06-04             <PERSON><PERSON><PERSON>A-5780  Called custom Mmap function
 */

package query

import (
	bytes2 "bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/VividCortex/ewma"
	"github.com/dolthub/swiss"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"runtime"
	"strings"
	"sync/atomic"
	"time"
)

/*----------------------------------------------------------------------------------------------------------------*/

// Executor is responsible for managing query execution, including parsing, distributing work,
// aggregating results, and managing memory resources. It coordinates with Worker instances
// to process queries efficiently.

// Start initializes the Executor and starts its main processing loop.
// It sets up all required components like logger, parser, memory pool manager, etc.,
// and launches a goroutine to handle incoming requests.
//
// Parameters:
//   - workers: Array of Worker instances that will process query tasks
//   - availableWorkers: Array of atomic booleans indicating worker availability
func (executor *Executor) Start(workers []*Worker, availableWorkers []atomic.Bool) {

	executor.Logger = executor.initLogger()

	executor.logger.Info(fmt.Sprintf("starting query executor for query engine type %v", executor.queryEngineType))

	executor.Parser = executor.initParser()

	executor.MemoryPoolManager = executor.initMemoryPoolManager()

	executor.ExecutorManager = executor.initExecutorManager()

	executor.EventManager = executor.initEventManager(workers)

	executor.Distributor = executor.initDistributor(workers, availableWorkers)

	executor.ResponseManager = executor.initResponseManager()

	if executor.queryEngineType == DrillDown {

		executor.DrillDownManager = executor.initDrillDownManager()
	}

	go func() {

		utils.QueryEngineShutdownMutex.Add(1)

		defer utils.QueryEngineShutdownMutex.Done()

		for !executor.shutdown && !utils.GlobalShutdown {

			executor.run()
		}

	}()
}

// run is the main event loop for the Executor.
// It handles various events like timer ticks for memory pool shrinking,
// incoming query requests, abort requests, and overflow notifications.
// This function runs in a separate goroutine and continues until shutdown is requested.
func (executor *Executor) run() {

	timer := time.NewTicker(time.Second * time.Duration(utils.GetMemoryPoolShrinkTimerSeconds()))

	defer func() {

		if err := recover(); err != nil {

			timer.Stop()

			stackTraceBytes := make([]byte, 1<<20)

			executor.logger.Error(fmt.Sprintf("error %v occurred in executor %v", err, executor.executorId))

			executor.logger.Error(fmt.Sprintf("!!!STACK TRACE for executor %v!!! \n %v", executor.executorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			if executor.overflowedWorkerId != utils.NotAvailable {

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("panic in do aggregation overflow for worker %v , hence notifying worker", executor.overflowedWorkerId))
				}

				executor.OverflowedAcks[executor.overflowedWorkerId] <- executor.overflowedWorkerId

				executor.overflowedWorkerId = utils.NotAvailable
			}

			finished := true

			for pendingEvents := range executor.workerPendingEvents.Values() {

				if pendingEvents > 0 {

					finished = false

					break
				}
			}

			if finished {

				if executor.preAggregationJobId == utils.NotAvailable {

					executor.progress = 100

					executor.dispatchErrors(errors.New(fmt.Sprintf("%v", err)))

					executor.NotifySubscriber(false)

				} else {

					utils.AggregationJobQueryAcks[executor.preAggregationJobId] <- executor.executorId
				}
			}
		}
	}()

	for {

		select {

		case <-timer.C:

			executor.memoryPool.ShrinkPool()

			executor.incrementalAggregationFuncMemoryPool.ShrinkPool()

		case request := <-executor.Requests:

			executor.queryTimestamp = time.Now().UnixMilli()

			executor.executeQueryRequest(request)

		case notification := <-executor.RetentionChangeNotifications:

			for datastoreType, context := range notification {

				retention := [2]int64{}

				retention[0] = int64(context[utils.Raw])

				retention[1] = int64(context[utils.Aggregated])

				if retention[0] == 0 {

					retention[0] = retention[1]

				} else if retention[1] == 0 {

					retention[1] = retention[0]
				}

				executor.retentionContext[utils.DatastoreType(datastoreType)] = retention
			}

		case subQueryId := <-executor.AbortRequests:

			if len(executor.subQueryId) > 0 && strings.EqualFold(executor.subQueryId, subQueryId) {

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("query abort received for query id %v", subQueryId))
				}

				executor.aborted = true

				for _, workerId := range executor.usedWorkers[:executor.usedWorkerElementSize] {

					if !executor.workerStates[workerId] {

						executor.workerStates[workerId] = true

						executor.workers[workerId].queryAbort = true
					}
				}

				if executor.queryEngineType == AIOps {

					// notify AIOps process to abort query
					executor.sendAIOpsAbortRequest()

					executor.NotifySubscriber(true)
				}
			}

		case event := <-executor.OverflowNotifications:

			utils.Split(event, utils.GroupSeparator, executor.tokenizers[0])

			workerId := StringToINT(executor.tokenizers[0].Tokens[1])

			if len(executor.subQueryId) > 0 && strings.EqualFold(executor.subQueryId, executor.tokenizers[0].Tokens[0]) {

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("starting do aggregation, reason : worker %v overflow notification received..", executor.tokenizers[0].Tokens[1]))
				}

				executor.overflowedWorkerId = workerId

				executor.workerStates[workerId] = true

				_ = executor.aggregate(false)

				executor.workerStates[workerId] = false

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("do aggregation done for overflow worker %v and notifying to continue pending tasks..", executor.tokenizers[0].Tokens[1]))
				}

				executor.OverflowedAcks[workerId] <- workerId

				executor.overflowedWorkerId = utils.NotAvailable

				continue
			}

			if utils.QueryPlanLogging {

				executor.logQueryPlan(fmt.Sprintf("worker %v overflow received but skipped due to query %v abort", workerId, executor.tokenizers[0].Tokens[1]))
			}

			executor.OverflowedAcks[workerId] <- workerId

		case response := <-executor.AIOpsEngineResponses:

			// ai engine query response

			executor.processAIOpsEngineResult(bytes2.NewBuffer(response))

			executor.NotifySubscriber(true)

		case response := <-executor.queryResponses:

			executor.processResponse(response)

		case _ = <-executor.ShutdownNotifications:

			executor.logger.Info(fmt.Sprintf("query executor %v has been shutdown...", executor.executorId))

			for index := range executor.ValueBuffers {

				if executor.ValueBuffers[index] == nil {

					continue
				}

				if err := utils.Munmap(executor.ValueBuffers[index]); err != nil {

					executor.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
				}
			}

			if executor.preAggregationJobId != utils.NotAvailable {

				utils.AggregationJobQueryAcks[executor.preAggregationJobId] <- executor.executorId
			}

			executor.memoryPool.Unmap()

			executor.incrementalAggregationFuncMemoryPool.Unmap()

			for i := range executor.encoders {

				if executor.encoders[i].MemoryPool != nil {

					executor.encoders[i].MemoryPool.Unmap()
				}
			}

			executor.shutdown = true

			return

		}
	}
}

// executeQueryRequest processes an incoming query request.
// It parses the query, distributes work to available workers, and initiates the query execution process.
// If any errors occur during parsing or distribution, they are dispatched to the appropriate handlers.
//
// Parameters:
//   - request: A MotadataMap containing the query parameters and configuration
func (executor *Executor) executeQueryRequest(request utils.MotadataMap) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			executor.logger.Error(fmt.Sprintf("error %v occurred in executor %v", err, executor.executorId))

			executor.logger.Error(fmt.Sprintf("!!!STACK TRACE for executor %v!!! \n %v", executor.executorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			if executor.preAggregationJobId == utils.NotAvailable {

				executor.progress = 100

				executor.dispatchErrors(errors.New(fmt.Sprintf("%v", err)))

				executor.NotifySubscriber(false)

			} else {

				utils.AggregationJobQueryAcks[executor.preAggregationJobId] <- executor.executorId
			}
		}
	}()

	if utils.TraceEnabled() {

		bytes, _ := json.Marshal(request)

		executor.logger.Trace(fmt.Sprintf("request %v received on query executor %v", string(bytes), executor.executorId))
	}

	err := executor.parseQuery(request)

	if err != nil {

		// dispatch error
		executor.dispatchParsingErrors(err)

		return
	}

	if executor.queryEngineType != DrillDown {

		err = executor.distributeWorkerEvents()

		if err != nil {

			// dispatch error
			executor.dispatchParsingErrors(err)

			return
		}

		executor.notifyWorkers()
	}
}

// parseQuery extracts and processes query parameters from the request.
// It sets up the query configuration including query ID, category, filters, and other properties.
// The function determines the query type (histogram, topN, etc.) and configures the execution accordingly.
//
// Parameters:
//   - request: A MotadataMap containing the query parameters and configuration
//
// Returns:
//   - error: An error if parsing fails, nil otherwise
func (executor *Executor) parseQuery(request utils.MotadataMap) error {
	// Extract basic query identification information
	// These IDs are used for tracking and correlating query execution
	executor.queryId = request.GetStringValue(QueryId)
	executor.subQueryId = request.GetStringValue(SubQueryId)

	// Set the visualization category which determines how results will be presented
	// Categories like Histogram, TopN, etc. require different processing approaches
	executor.category = request.GetStringValue(VisualizationCategory)

	// Enable bitmap filtering by default for efficient data filtering
	// Bitmap filters provide significant performance improvements for large datasets
	executor.bitmapFilter = true

	// Enable result publishing by default
	// This controls whether results are sent back to the client
	executor.publish = true

	// Log query planning information if enabled
	// This is useful for debugging and performance analysis
	if utils.QueryPlanLogging {
		executor.logQueryPlan(fmt.Sprintf("request received to parse query for query id %v and sub query id %v", executor.queryId, executor.subQueryId))
	}

	// Extract visualization properties and data sources
	// These contain configuration for how the query should be executed and results presented
	visualizationProperties := request.GetMapValue(VisualizationProperties)
	dataSource := request.GetMapValue(VisualizationDataSources)

	// Determine if this is a histogram query
	// Histogram queries require special handling for time-series data
	histogramQuery := false

	// Complex logic to determine if this is a histogram query based on query step and category
	// This is a critical decision point that affects the entire query execution path
	if request.Contains(LastQueryStep) && strings.EqualFold(request.GetStringValue(LastQueryStep), utils.Yes) && !executor.topNIncrementalQuery {
		// This is step 2 of a multi-step query (e.g., TopN with sparkline)
		// In step 2, we're processing the histogram/time-series part
		histogramQuery = true
	} else {
		// This is either step 1 of a multi-step query or a single-step query
		// Mark this as the last step by default, may be changed below
		request[LastQueryStep] = utils.Yes

		// For time-series visualization categories, determine if this is a histogram query
		if executor.category == Histogram || executor.category == Stream || executor.category == Anomaly || executor.category == Forecast || executor.category == Baseline {
			if dataSource.Contains(VisualizationResultBy) {
				// If result grouping is specified, this is step 1 of a multi-step query
				// We'll need another query to get the time-series data for each group
				request[LastQueryStep] = utils.No
			} else {
				// No grouping, so this is a direct histogram query
				histogramQuery = true
			}
		} else if executor.category == TopN && visualizationProperties.Contains(Sparkline) && visualizationProperties.GetStringValue(Sparkline) == utils.Yes {
			// TopN with sparkline requires two steps:
			// 1. Get the top N entities
			// 2. Get time-series data for those entities
			// This marks step 1, indicating we need step 2 later
			request[LastQueryStep] = utils.No
		}
	}

	// Check if this query is part of an aggregation job
	// Aggregation jobs have special handling for performance optimization
	if request.Contains(utils.AggregationJobId) {
		// Store the aggregation job ID for later use
		// This allows coordination with background aggregation processes
		executor.preAggregationJobId = int8(request.GetIntValue(utils.AggregationJobId))
	} else {
		// Not an aggregation job
		executor.preAggregationJobId = utils.NotAvailable
	}

	// Extract and process timeline information
	// This defines the time range for the query
	visualizationTimeline := request.GetMapValue(VisualizationTimeline)

	// Convert Unix timestamps to time.Time objects
	executor.fromDateTime = utils.UnixMillisToTime(visualizationTimeline.GetInt64Value(FromDateTime))
	executor.toDateTime = utils.UnixMillisToTime(visualizationTimeline.GetInt64Value(ToDateTime))

	// Check if this is a time window query
	// Time window queries operate on specific time buckets rather than raw timestamps
	executor.timeWindowQuery = visualizationTimeline.GetStringValue(VisualizationTimeWindowCheck) == utils.Yes

	// For time window queries, calculate the start and end positions
	// These positions represent time buckets in the storage system
	if executor.timeWindowQuery {
		// Convert timestamps to tick positions (time buckets)
		executor.timeWindowStartPosition = int32(utils.CalculateTickPosition(utils.UnixToSeconds(executor.fromDateTime.Unix())))
		executor.timeWindowEndPosition = int32(utils.CalculateTickPosition(utils.UnixToSeconds(executor.toDateTime.Unix())))

		// Validate that the time window is valid (start before end)
		// This prevents logical errors in time range processing
		if executor.timeWindowStartPosition >= executor.timeWindowEndPosition {
			return errors.New("invalid timeline for time window query as start position is less than end position")
		}
	}

	// Log timeline information if query plan logging is enabled
	if utils.QueryPlanLogging {
		executor.logQueryPlan(fmt.Sprintf("from date time is %v and to date time is %v", executor.fromDateTime.String(), executor.toDateTime.String()))
	}

	// Calculate the duration of the query in seconds
	// This is used for granularity calculations and validation
	duration := int(executor.toDateTime.Unix() - executor.fromDateTime.Unix())

	// Validate that the duration is positive
	// This prevents logical errors in time range processing
	if duration <= 0 {
		return errors.New("failed to execute query, reason : timeline is not proper")
	}

	// Set the datastore type based on the data source
	// Different datastore types require different query handling
	executor.datastoreType = utils.DatastoreType(dataSource.GetIntValue(utils.Type))

	// Validate the query request
	// This checks various parameters and constraints
	var err error

	err, histogramQuery = executor.validateQueryRequest(dataSource, request, histogramQuery)

	if err != nil {
		return err
	}

	// Get the data points to be queried
	// These define what metrics or fields to retrieve
	dataPoints := dataSource.GetMapListValue(DataPoints)

	// Calculate the appropriate time granularity for the query
	// This determines how data points are grouped in time
	granularity := executor.calculateGranularity(request, histogramQuery, duration, len(dataPoints))

	// Store the calculated granularity in the request for later use
	request[VisualizationGranularitySeconds] = granularity

	// Log the applied granularity if query plan logging is enabled
	if utils.QueryPlanLogging {
		executor.logQueryPlan(fmt.Sprintf("applied granularity is %v", granularity))
	}

	// Store the processed request for later use during execution
	executor.request = request

	if executor.datastoreType == utils.PerformanceMetric || executor.datastoreType == utils.ObjectStatusMetric || executor.datastoreType == utils.ObjectStatusFlapMetric || executor.datastoreType == utils.TrapFlapHistory ||
		executor.datastoreType == utils.NetRouteMetric || executor.datastoreType == utils.NetRouteStatusMetric || executor.datastoreType == utils.SLOMetric || executor.datastoreType == utils.SLOFlapMetric {

		return executor.parseHybridQuery(dataSource, dataPoints, dataSource.GetSliceValue(utils.Plugins), histogramQuery)

	} else if executor.datastoreType == utils.EventPolicy {

		// check whether vertical suits for this query or not
		if executor.plugin == datastore.TrapPolicyPlugin || executor.isHorizontalQuery(dataSource, utils.PolicyId) {

			return executor.parseHorizontalQuery(dataSource, dataPoints, dataSource.GetSliceValue(utils.Plugins), histogramQuery)
		} else {

			return executor.parseHybridQuery(dataSource, dataPoints, dataSource.GetSliceValue(utils.Plugins), histogramQuery)
		}

	}

	return executor.parseHorizontalQuery(dataSource, dataPoints, dataSource.GetSliceValue(utils.Plugins), histogramQuery)
}

// dispatchParsingErrors handles errors that occur during query parsing.
// It has special handling for DrillDown queries, where it may need to update progress
// and move on to unprocessed dates. For other query types, it dispatches the error
// and notifies subscribers.
//
// Parameters:
//   - err: The error that occurred during parsing
func (executor *Executor) dispatchParsingErrors(err error) {

	if executor.queryEngineType == DrillDown {

		// parsing error

		//in incremental drill down parsing there can be an instance where for a particular date the filter doesn't qualify,
		//hence we need to update the progress and move on to the unprocessed dates until all dates are processed.
		if utils.DebugEnabled() {

			executor.queryLogger.Debug(fmt.Sprintf("err %v occurred while parsing drill down query...", err))
		}

		err = errors.New(utils.Empty)

		if executor.progress < 100 {

			event := executor.drillDownEvent

			event.LastDatePosition++

			executor.progress = float64((event.LastDatePosition)*100) / float64(len(event.QualifiedDates))

			if executor.progress > 100 {

				executor.progress = 100
			}

			if utils.QueryPlanLogging {

				executor.logQueryPlan(fmt.Sprintf("incrementing current date position to: %v, current progress: %v", event.LastDatePosition, executor.progress))
			}

			if executor.progress == 100 {

				executor.notify(false, 0)

				searchQuery := utils.DatastoreType(executor.request.GetMapValue(VisualizationDataSources).GetIntValue(utils.Type)) == utils.Log

				executor.logQueryPlan("query completed dispatching empty error response...")

				executor.dispatchErrors(err)

				if !searchQuery {

					executor.abortQuery()
				}

				executor.NotifySubscriber(true)

				return

			} else {

				executor.logQueryPlan("completing query....")

				executor.notify(true, event.QualifiedDates[event.LastDatePosition])

				if err = executor.completeQuery(); err == nil {

					return
				}
			}

		}
	}

	if executor.preAggregationJobId == utils.NotAvailable {

		if utils.QueryPlanLogging {

			executor.logQueryPlan(fmt.Sprintf("err is %v", err.Error()))
		}

		executor.progress = 100

		executor.dispatchErrors(err)

		executor.NotifySubscriber(true)

	} else {

		executor.logError(err.Error())

		utils.AggregationJobQueryAcks[executor.preAggregationJobId] <- executor.executorId
	}
}

/* --------------------------------------------- Helper Functions ------------------------------------------------------------ */

// calculateEMA calculates the Exponential Moving Average for the given groups of data.
// It supports both int64 and float64 data types and handles cases where there are insufficient
// data points for a reliable calculation.
//
// Parameters:
//   - records: The number of records per group
//   - groups: Array of group identifiers for which to calculate EMA
func (executor *Executor) calculateEMA(records int, groups []uint64) {

	if len(groups) > 0 {

		periods := 0

		if executor.ColumnPoolDataTypes[0] == Int64 {

			poolIndex, values := executor.memoryPool.AcquireINT64Pool(len(groups))

			executor.ColumnPoolIndices[BaseLineColumnIndex] = poolIndex

			executor.ColumnPoolDataTypes[BaseLineColumnIndex] = Int64

			executor.ColumnElementSize += 1

			executor.ColumnPoolElementSize += 1

			for i := range groups {

				offset, _ := executor.Groups.Get(groups[i])

				movingAverage := ewma.NewMovingAverage(float64(movingAveragePeriods))

				for _, value := range executor.memoryPool.GetINT64Pool(executor.ColumnPoolIndices[0])[offset : offset+records] {

					if value != utils.DummyINT64Value {

						periods++

						movingAverage.Add(float64(value))
					}
				}

				if periods < movingAveragePeriods {

					values[i] = utils.DummyINT64Value

					executor.logError(utils.ErrorBaselineInvalidDataPoint)

				} else {

					values[i] = int64(movingAverage.Value())
				}

			}

		} else {

			poolIndex, values := executor.memoryPool.AcquireFLOAT64Pool(len(groups))

			executor.ColumnPoolIndices[BaseLineColumnIndex] = poolIndex

			executor.ColumnPoolDataTypes[BaseLineColumnIndex] = Float64

			executor.ColumnPoolElementSize += 1

			executor.ColumnElementSize += 1

			periods = 0

			for i := range groups {

				offset, _ := executor.Groups.Get(groups[i])

				movingAverage := ewma.NewMovingAverage(float64(movingAveragePeriods))

				for _, value := range executor.memoryPool.GetFLOAT64Pool(executor.ColumnPoolIndices[0])[offset : offset+records] {

					if value != utils.DummyFLOAT64Value {

						movingAverage.Add(value)

						periods++
					}
				}

				if periods < movingAveragePeriods {

					values[i] = utils.DummyFLOAT64Value

					executor.logError(utils.ErrorBaselineInvalidDataPoint)

				} else {

					values[i] = movingAverage.Value()

				}

			}
		}
	}
}

// calculateGranularity determines the appropriate time granularity for query results.
// It considers the query type, visualization category, duration, and number of data points
// to calculate an optimal granularity that balances detail and performance.
//
// Parameters:
//   - request: The query request containing visualization parameters
//   - histogramQuery: Whether this is a histogram query
//   - duration: The time duration of the query in seconds
//   - dataPoints: The number of data points in the result
//
// Returns:
//   - int: The calculated granularity in seconds
func (executor *Executor) calculateGranularity(request utils.MotadataMap, histogramQuery bool, duration int, dataPoints int) int {

	granularity := utils.NotAvailable

	if histogramQuery {

		maxSeries := utils.MaxSparklineTicks

		if executor.category == Forecast && executor.visualizationResultType == int8(Extended) {

			maxSeries = utils.MaxAIOpsTicks
		}

		if executor.category == Histogram {

			maxSeries = utils.MaxLineChartTicks

			if strings.HasSuffix(request.GetStringValue(visualizationType), "Bar") {
				maxSeries = utils.MaxBarChartTicks
			}

			if !request.Contains(VisualizationResultBy) {

				maxSeries = maxSeries / dataPoints
			}
		}

		granularity = 300

		if request.Contains(VisualizationGranularity) {

			visualizationGranularity := request.GetStringValue(VisualizationGranularity)

			if strings.HasSuffix(visualizationGranularity, "s") {

				utils.Split(visualizationGranularity, "s", executor.tokenizers[0])

				granularity = StringToINT(executor.tokenizers[0].Tokens[0])

			} else if strings.HasSuffix(visualizationGranularity, "m") {

				utils.Split(visualizationGranularity, "m", executor.tokenizers[0])

				granularity = StringToINT(executor.tokenizers[0].Tokens[0]) * 60

			} else if strings.HasSuffix(visualizationGranularity, "h") {

				utils.Split(visualizationGranularity, "h", executor.tokenizers[0])

				granularity = StringToINT(executor.tokenizers[0].Tokens[0]) * 3600

			} else if strings.HasSuffix(visualizationGranularity, "d") {

				utils.Split(visualizationGranularity, "d", executor.tokenizers[0])

				granularity = StringToINT(executor.tokenizers[0].Tokens[0]) * 86400

			} else if strings.HasSuffix(visualizationGranularity, "M") {

				utils.Split(visualizationGranularity, "M", executor.tokenizers[0])

				granularity = StringToINT(executor.tokenizers[0].Tokens[0]) * 86400 * 30
			}

			// in custom timeline motadata is not giving duration so everytime granularity is 300 and for 5 years query we can't override 300 second granularity as timeticks is always 0 as duration is not coming
			timeTicks := duration / granularity

			index := 0

			valid := false

			for !valid {

				index++

				if timeTicks <= (maxSeries * index) {

					valid = true

					granularity = granularity * index
				}
			}

		} else {

			// in seconds
			granularity = int(executor.toDateTime.Unix()-executor.fromDateTime.Unix()) / maxSeries

			if granularity <= 300 {

				granularity = 300

			} else if granularity <= 1800 {

				granularity = 1800

			} else if granularity <= 3600 {

				granularity = 3600

			} else if granularity <= 21600 {

				granularity = 21600
			}
		}
	}

	return granularity
}

// chooseAggregationTimeInterval selects an appropriate time interval for data aggregation
// based on the query time range and query type.
//
// Parameters:
//   - fromTick: The starting timestamp (in seconds)
//   - toTick: The ending timestamp (in seconds)
//   - fulltextSearchingView: Whether this is a fulltext search view
//
// Returns:
//   - int: The selected aggregation interval in minutes
func (executor *Executor) chooseAggregationTimeInterval(fromTick, toTick int32, fulltextSearchingView bool) int {

	difference := int(toTick - fromTick)

	if executor.queryEngineType == Metric || executor.queryEngineType == AIOps {

		return chooseTimeInterval(utils.AggregationIntervals, difference)
	}

	if fulltextSearchingView {

		if difference <= 3600 { // upto 1 hour 5 mins interval

			return utils.FullTextViewAggregationIntervals[0]

		} // >1 hour 30 mins interval

		return utils.FullTextViewAggregationIntervals[1]
	}

	return chooseTimeInterval(utils.EventAggregationIntervals, difference)

}

// applyGranularity applies the appropriate time granularity to the query.
// It logs query planning metrics and adjusts the granularity based on query type
// and pre-aggregation settings.
//
// Parameters:
//   - histogramQuery: Whether this is a histogram query
//   - timestamp: The timestamp when query planning started (for performance measurement)
func (executor *Executor) applyGranularity(histogramQuery bool, timestamp int64) {

	if utils.QueryPlanLogging {

		if executor.preAggregationQuery {

			executor.logQueryPlan(fmt.Sprintf("query planner took %v ms to qualify %v aggregated keys", time.Now().UnixMilli()-timestamp, executor.keyElementSize))
		} else {

			executor.logQueryPlan(fmt.Sprintf("query planner took %v ms to qualify %v raw keys", time.Now().UnixMilli()-timestamp, executor.keyElementSize))
		}
	}

	granularity := int32(utils.NotAvailable)

	if histogramQuery {

		granularity = executor.request.GetInt32Value(VisualizationGranularitySeconds)
	}

	if granularity != utils.NotAvailable && !executor.drillDownQuery {

		// BUG ID - MOTADATA-1812
		if executor.request.GetIntValue(VisualizationGranularitySeconds) > 0 && executor.preAggregationQuery &&
			int(granularity/60) < executor.preAggregationTimeInterval {

			granularity = int32(executor.preAggregationTimeInterval * 60)

			executor.request[VisualizationGranularitySeconds] = granularity
		}

		executor.startTick = utils.CalculateHistogramTick(time.Date(executor.fromDateTime.Year(), executor.fromDateTime.Month(), executor.fromDateTime.Day(), 0, 0, 0, 0, time.UTC).Unix(), int64(granularity))

		executor.endTick = utils.CalculateHistogramTick(executor.toDateTime.Unix(), int64(granularity))

		if utils.QueryPlanLogging {

			executor.logQueryPlan(fmt.Sprintf("start tick %v and end tick %v calculated for granularity %v seconds", executor.startTick, executor.endTick, granularity))
		}
	}

	executor.granularity = granularity

}

// getParts determines the number of parts for a given hash in a storage store.
// If the store contains multipart keys, it returns the maximum part number plus one.
// Otherwise, it returns 1.
//
// Parameters:
//   - hash: The hash value to check
//   - store: The storage store to query
//
// Returns:
//   - int: The number of parts
func getParts(hash uint64, store *storage.Store) int {

	if store.ContainsMultipartKey() {

		return int(store.GetMaxPart(hash)) + 1
	}

	return 1
}

// chooseTimeInterval selects the most appropriate time interval from a list of intervals
// based on the time difference. It uses a heuristic approach to balance between detail and performance.
//
// Parameters:
//   - intervals: Array of available time intervals in minutes
//   - difference: The time difference in seconds
//
// Returns:
//   - int: The selected time interval in minutes
func chooseTimeInterval(intervals []int, difference int) int {

	//difference is in seconds and intervals are in minutes
	//hence dividing the difference by 60 to convert it in minutes then the number is divided by the lowest interval
	//the resulting number will be the number of ticks qualified for this query.
	//as in horizontal the key is based on the tick.

	probes := (difference / 60) / intervals[0]

	if len(intervals) == 3 {

		//considered using the lowest interval to be 5 minutes
		//72*60*5 = 21600 seconds
		if probes <= 72 { //for 72 probes meaning difference is less than 21600 seconds (less than 6 hours)

			return intervals[0]

		} else if probes <= 576 { //greater than 6 hours and less than 2 days (48 hours)

			return intervals[1]
		}

		return intervals[2]

	} else if len(intervals) == 2 {

		if probes <= 72 { //less than 8 hours

			return intervals[0]
		}

		return intervals[1]

	} else {

		return intervals[0]
	}

}

func (executor *Executor) validateAIOPsRequest(request utils.MotadataMap) error {

	if !request.Contains(visualizationResultType) {

		return errors.New(utils.ErrorInvalidVisualizationType)
	}

	executor.visualizationResultType = int8(request.GetIntValue(visualizationResultType))

	if executor.category == Forecast {

		// forecast support Compacted , Normalized , Extended visualization result type

		if !(executor.visualizationResultType == int8(Compacted) || executor.visualizationResultType == int8(Normalized) || executor.visualizationResultType == int8(Extended)) {

			return errors.New(utils.ErrorInvalidVisualizationType)
		}
	} else if executor.category == Anomaly {

		// Anomaly support Compacted , Normalized , Extended visualization result type

		if !(executor.visualizationResultType == int8(Normalized) || executor.visualizationResultType == int8(Extended)) {

			return errors.New(utils.ErrorInvalidVisualizationType)
		}

	} else if executor.category == Baseline {

		// Baseline support Compacted visualization result type

		if executor.visualizationResultType != int8(Compacted) {

			return errors.New(utils.ErrorInvalidVisualizationType)
		}

	}

	return nil
}

func (executor *Executor) validateQueryRequest(dataSource, request utils.MotadataMap, histogramQuery bool) (error, bool) {

	count := 0

	plugins := dataSource.GetSliceValue(utils.Plugins)

	// validate plugins
	if len(plugins) == 0 {

		return errors.New(utils.ErrorPluginRequired), histogramQuery
	}

	executor.plugin = ToString(plugins[0])

	// validate data-points
	if (executor.datastoreType == utils.ObjectStatusFlapMetric || executor.datastoreType == utils.StatusFlapHistory) && !strings.Contains(executor.plugin, utils.Duration) {

		executor.enrichPlugin(dataSource, plugins, utils.Duration, count)

	} else if executor.datastoreType == utils.SLOMetric && strings.Contains(executor.plugin, datastore.SLOInstancePlugin) && !strings.Contains(executor.plugin, request.GetStringValue(StoreSuffix)) {

		executor.enrichPlugin(dataSource, plugins, request.GetStringValue(StoreSuffix), count)

	} else if executor.datastoreType == utils.SLOFlapMetric && strings.Contains(executor.plugin, datastore.SLOInstancePlugin) && !strings.Contains(executor.plugin, request.GetStringValue(StoreSuffix)+utils.DotSeparator+utils.Duration) {

		executor.enrichPlugin(dataSource, plugins, request.GetStringValue(StoreSuffix)+utils.DotSeparator+utils.Duration, count)

	} else {

		for _, dataPoint := range dataSource.GetMapListValue(DataPoints) {

			column := dataPoint.GetStringValue(DataPoint)

			if datastore.IsShadowCounter(column) {

				column = datastore.FlipShadowCounter(column)

				entityKeys := dataPoint.GetMapValue(EntityKeys)

				/* when entity keys is present we need to replace the column present in entity keys with the alias counter column.
				 In case of single monitor only entity keys will be there from motadata side.
				From datastore side entity keyps will be there in step 2 query but with limited number of monitors applied in the sorting ( max 50)*/

				if entityKeys != nil {

					qualifiedEntities := make(utils.MotadataMap, len(entityKeys))

					if strings.Contains(column, utils.InstanceSeparator) {

						for key, value := range entityKeys {

							utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

							executor.tokenizers[0].Tokens[2] = datastore.FlipShadowCounter(executor.tokenizers[0].Tokens[2])

							qualifiedEntities[strings.Join(executor.tokenizers[0].Tokens[:executor.tokenizers[0].Counts], utils.KeySeparator)] = value
						}
					} else {

						for key, value := range entityKeys {

							utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

							executor.tokenizers[0].Tokens[1] = datastore.FlipShadowCounter(executor.tokenizers[0].Tokens[1])

							qualifiedEntities[strings.Join(executor.tokenizers[0].Tokens[:executor.tokenizers[0].Counts], utils.KeySeparator)] = value
						}

					}

					entityKeys = qualifiedEntities

					dataPoint[EntityKeys] = entityKeys

				}

			}

			aggregator := dataPoint.GetStringValue(Aggregator)

			// topN last with sparkline changed aggregation of sparkline from last to avg
			if dataPoint.Contains(Sparkline) && dataPoint.GetStringValue(Sparkline) == utils.Yes && aggregator == LastFunc {

				aggregator = AvgFunc

				dataPoint[Aggregator] = AvgFunc
			}

			if aggregator == AvgFunc {

				count = count + 2

			} else if aggregator != utils.Empty {

				count++
			} else {

				executor.drillDownQuery = true
			}
		}
	}

	if count > 18 {

		return errors.New(utils.ErrorInvalidDataPoint), histogramQuery
	}

	// validate AIOPs query request
	if executor.queryEngineType == AIOps {

		err := executor.validateAIOPsRequest(request)

		if err != nil {

			return err, histogramQuery
		}
	}

	// validate passover step1 query
	if (request.Contains(PassOverStep1Query) && request.GetStringValue(PassOverStep1Query) == utils.Yes) && (executor.queryEngineType == Metric || executor.queryEngineType == AIOps) {

		request[LastQueryStep] = utils.Yes

		histogramQuery = true

		request[AdminRole] = utils.No

		if len(dataSource.GetMapValue(Entities)) > 50 {

			return errors.New(utils.ErrorPassoverStep1EntitiesLimitExceeded), histogramQuery
		}
	}

	// validate plugins
	plugins = dataSource.GetSliceValue(utils.Plugins)

	if len(plugins) == 0 {

		return errors.New(utils.ErrorPluginRequired), histogramQuery
	}

	executor.plugin = ToString(plugins[0])

	for _, group := range dataSource.GetMapValue(Filters).GetMapValue(DataFilter).GetMapListValue(Groups) {

		for _, condition := range group.GetMapListValue(Conditions) {

			operand := condition.GetStringValue(Operand)

			if datastore.IsSearchableColumn(operand) || datastore.IsBlobColumn(operand) {

				executor.bitmapFilter = false
			}
		}
	}

	return nil, histogramQuery
}

func (executor *Executor) logQueryPlan(message string) {

	if !utils.QueryPlanLogging {

		return
	}

	executor.queryLogger.LogQueryPlan(message)
}

func (executor *Executor) logError(errs string) {

	if executor.errorElementSize == executor.poolLength || strings.Contains(errs, "not found for the store") || len(errs) == 0 {

		return
	}

	if utils.DebugEnabled() {

		executor.logger.Error(errs)
	}

	executor.errors[executor.errorElementSize] = errs

	executor.errorElementSize++
}

func (executor *Executor) NotifySubscriber(testPoolLeak bool) {

	queryId := executor.queryId

	subQueryId := executor.subQueryId

	if utils.QueryPlanLogging {

		if executor.preAggregationJobId == utils.NotAvailable {

			executor.logQueryPlan(fmt.Sprintf("query planner scanned %v records, total time elapsed %v ms", executor.records, time.Now().UnixMilli()-executor.queryTimestamp))

		} else {

			executor.logQueryPlan(fmt.Sprintf("query planner scanned %v records, total time elapsed %v ms for aggregated query", executor.records, time.Now().UnixMilli()-executor.queryTimestamp))
		}
	}

	executor.cleanup(false)

	executor.records = 0

	executor.preAggregationJobId = utils.NotAvailable

	if utils.Responses != nil {

		clear(executor.request)

		utils.Responses <- INTToStringValue(int(executor.queryEngineType)) + utils.KeySeparator + INTToStringValue(executor.executorId) + utils.KeySeparator + queryId + utils.KeySeparator + subQueryId
	}

	executor.progress = 0

	if testPoolLeak {

		executor.memoryPool.TestPoolLeak()

		executor.incrementalAggregationFuncMemoryPool.TestPoolLeak()

		for i := range executor.decoders {

			executor.decoders[i].MemoryPool.TestPoolLeak()

			executor.encoders[i].MemoryPool.TestPoolLeak()
		}
	}
}

func (executor *Executor) enrichPlugin(dataSource utils.MotadataMap, plugins []interface{}, suffix string, count int) {

	qualifiedEntities := utils.MotadataMap{}

	for _, dataPoint := range dataSource.GetMapListValue(DataPoints) {

		aggregator := dataPoint.GetStringValue(Aggregator)

		if aggregator == AvgFunc {

			count = count + 2

		} else if aggregator != utils.Empty {

			count++
		} else {

			executor.drillDownQuery = true
		}

		plugins := dataPoint.GetSliceValue(utils.Plugins)

		for index := range plugins {

			plugins[index] = ToString(plugins[index]) + utils.DotSeparator + suffix
		}

		clear(qualifiedEntities)

		for key, value := range dataPoint.GetMapValue(Entities) {

			qualifiedEntities[key] = ToString(value) + utils.DotSeparator + suffix
		}

		dataPoint[Entities] = qualifiedEntities

		entityKeys := dataPoint.GetMapValue(EntityKeys)

		if entityKeys != nil && len(entityKeys) > 0 {

			clear(qualifiedEntities)

			for key, value := range entityKeys {

				qualifiedEntities[key] = ToString(value) + utils.DotSeparator + suffix
			}

			dataPoint[EntityKeys] = qualifiedEntities
		}
	}

	for index := range plugins {

		plugins[index] = ToString(plugins[index]) + utils.DotSeparator + suffix
	}

	clear(qualifiedEntities)

	for key, value := range dataSource.GetMapValue(Entities) {

		qualifiedEntities[key] = ToString(value) + utils.DotSeparator + suffix
	}

	dataSource[Entities] = qualifiedEntities

	entityKeys := dataSource.GetMapValue(EntityKeys)

	if entityKeys != nil && len(entityKeys) > 0 {

		clear(qualifiedEntities)

		for key, value := range entityKeys {

			qualifiedEntities[key] = ToString(value) + utils.DotSeparator + suffix
		}

		dataSource[EntityKeys] = qualifiedEntities
	}
}

/* --------------------------------------------- Cleanup Functions ------------------------------------------------------------ */

func (executor *Executor) cleanup(partial bool) {

	if partial {

		executor.cleanupStep1()

		if !executor.topNIncrementalQuery {

			clear(executor.conditionOperands)
		}

		return
	}

	executor.cleanupStep1()

	executor.fulltextSearchingView = false

	for index := range executor.statisticalFuncs {

		executor.statisticalFuncs[index] = utils.Empty
	}

	executor.shadowCounterIndices.Clear()

	clear(executor.conditionOperands)

	for _, bitmap := range executor.MappingHelperBitmaps {

		if bitmap != nil {

			bitmap.Clear()
		}
	}

	clear(executor.drillDownFilter)

	clear(executor.helperTicks)

	clear(executor.helperDates)

	executor.topNIncrementalQuery = false

	executor.topNIncrementalGroupIndex = 0

	executor.conditionExpression = utils.Empty

	executor.helperConditionExpression = utils.Empty

	executor.errorElementSize = 0

	executor.records = 0

	executor.queryId = utils.Empty

	executor.conditionOperand = utils.Empty

	executor.mappingOperand = utils.Empty

	executor.subQueryId = utils.Empty

	if executor.queryEngineType == DrillDown {

		if executor.qualifiedTickPoolIndex != utils.NotAvailable {

			executor.memoryPool.ReleaseINT32Pool(executor.qualifiedTickPoolIndex)
		}

		executor.qualifiedTickPoolIndex = utils.NotAvailable

		executor.qualifiedTicks = nil

		executor.qualifiedTickElementSize = 0

		executor.tickPosition = 0

		executor.incrementalQuery = false

		executor.paginationQuery = false

		executor.pageSize = 0

		executor.pageIndex = 0

		executor.timestamp = 0

		executor.qualifiedDateIndex = 0

		executor.searchEventId = utils.Empty
	}

	executor.packerBytePoolIndex = utils.NotAvailable

	executor.request.Clear()

	clear(executor.ordinals)

	clear(executor.conditionOperandValues)

	executor.ResolvedGroups.Clear()

	clear(executor.fields)
}

func (executor *Executor) cleanupStep1() {

	executor.Groups = nil

	executor.flapStatuses = nil

	executor.ResolvedGroups.Clear()

	executor.externalGroupFilterElementSize = 0

	executor.qualifiedStoreElementSize = 0

	executor.probes = 0

	executor.visualizationResultType = utils.NotAvailable

	executor.GroupColumnElementSize = 0

	executor.preAggregationTimeInterval = 0

	executor.ColumnElementSize = 0

	executor.currentStoreIndex = 0

	executor.externalGroupIndex = utils.NotAvailable

	executor.timeWindowQuery = false

	executor.timeBoundQuery = false

	executor.preAggregationQuery = false

	executor.instanceQuery = false

	executor.innerGroupingField = utils.Empty

	executor.outerGroupingField = utils.Empty

	executor.drillDownQuery = false

	executor.preFilterQuery = false

	executor.externalInstanceGrouping = false

	executor.resultFilter = false

	executor.lookupOrdinal = false

	executor.bitmapFilter = false

	executor.aborted = false

	executor.publish = false

	executor.category = utils.Empty

	executor.plugin = utils.Empty

	executor.value = utils.Empty

	executor.startTick = 0

	executor.endTick = 0

	executor.keyElementSize = 0

	executor.overflowedWorkerId = utils.NotAvailable

	executor.granularity = utils.NotAvailable

	clear(executor.categories)

	for _, workerId := range executor.usedWorkers[:executor.usedWorkerElementSize] {

		if workerId != utils.NotAvailable {

			if utils.QueryPlanLogging {

				executor.logQueryPlan(fmt.Sprintf("starting Cleanup for worker %v", workerId))
			}

			executor.workers[workerId].cleanupNotifications <- executor.executorId

			<-executor.CleanupAcks[workerId]

			executor.usedWorkers[executor.usedWorkerElementSize] = utils.NotAvailable
		}
	}

	executor.usedWorkerElementSize = 0

	executor.releaseBorrowedWorkers()

	clear(executor.aggregations)

	executor.entities.Clear()

	clear(executor.columnIndices)

	clear(executor.ticks)

	clear(executor.dates)

	for key, bitmap := range executor.externalGroupFilterOrdinals {

		bitmap.Clear()

		delete(executor.externalGroupFilterOrdinals, key)
	}

	executor.cleanupMapping()

	clear(executor.missingColumns)

	for i := range executor.helpers {

		executor.helpers[i] = 0
	}

	executor.cleanupPool()

	executor.ColumnPoolElementSize = 0

	executor.incrementalAggregationFuncPoolElementSize = 0

	executor.drillDownPosition = 0

	for i := 0; i < executor.poolSize; i++ {

		executor.helperPools[i] = utils.NotAvailable

		executor.ColumnPoolIndices[i] = utils.NotAvailable

		executor.ColumnPoolDataTypes[i] = Invalid

		executor.incrementalAggregationPoolDataTypes[i] = Invalid
	}
}

func (executor *Executor) cleanupPool() {

	if executor.keyPoolIndex != utils.NotAvailable {

		executor.memoryPool.ReleaseStringPool(executor.keyPoolIndex)

		executor.keyPoolIndex = utils.NotAvailable
	}

	executor.logQueryPlan("starting memory pool clean up..")

	for i := range executor.ColumnPoolDataTypes[:executor.ColumnPoolElementSize] {

		switch executor.ColumnPoolDataTypes[i] {

		case Int64:

			executor.memoryPool.ReleaseINT64Pool(executor.ColumnPoolIndices[i])

		case Float64:

			executor.memoryPool.ReleaseFLOAT64Pool(executor.ColumnPoolIndices[i])

		case String:

			executor.memoryPool.ReleaseStringPool(executor.ColumnPoolIndices[i])

		}

		if executor.helperPools[i] != utils.NotAvailable {

			executor.memoryPool.ReleaseINT64Pool(executor.helperPools[i])

			executor.helperPools[i] = utils.NotAvailable
		}

		executor.ColumnPoolDataTypes[i] = Invalid

		executor.ColumnPoolIndices[i] = utils.NotAvailable

	}

	for i := range executor.incrementalAggregationPoolDataTypes[:executor.ColumnPoolElementSize] {

		switch executor.incrementalAggregationPoolDataTypes[i] {

		case Int64:

			executor.incrementalAggregationFuncMemoryPool.ReleaseINT64Pool(executor.incrementalAggregationPoolIndices[i])

		case Float64:

			executor.incrementalAggregationFuncMemoryPool.ReleaseFLOAT64Pool(executor.incrementalAggregationPoolIndices[i])

			//case String:
			//
			//	executor.incrementalAggregationFuncMemoryPool.ReleaseStringPool(executor.incrementalAggregationPoolIndices[i])

		}

		executor.incrementalAggregationPoolDataTypes[i] = Invalid

		executor.incrementalAggregationPoolIndices[i] = utils.NotAvailable
	}
}

func (executor *Executor) cleanupMapping() {

	executor.stringOrdinalMappings.Clear()

	executor.numericOrdinalMappings.Clear()

	for i := range executor.stringMappings {

		executor.stringMappings[i].Clear()

		executor.numericMappings[i].Clear()
	}

	for _, bitmap := range executor.MappingBitmaps {

		if bitmap != nil {

			bitmap.Clear()
		}
	}

	for _, bitmap := range executor.DrillDownMappingBitmaps {

		if bitmap != nil {

			bitmap.Clear()
		}
	}
}

func (executor *Executor) releaseBorrowedWorkers() {

	if executor.workerPendingEvents.Len() > 0 {

		for worker := range executor.workerPendingEvents.Keys() {

			executor.availableWorkers[worker].Store(false)
		}

		executor.workerPendingEvents.Clear()
	}
}

func (executor *Executor) sendAIOpsAbortRequest() {

	buffer := bytes2.Buffer{}

	buffer.Write(AbortQueryBytes)

	EncodeINT16Value(int16(len(executor.subQueryId)), &buffer)

	buffer.WriteString(executor.subQueryId)

	utils.AIOpsEngineRequests <- buffer.Bytes()[:buffer.Len()]

	if utils.QueryPlanLogging {

		executor.logQueryPlan(fmt.Sprintf("Query abort request sent to AIOps engine for sub query id : %v", executor.subQueryId))
	}
}

func (executor *Executor) resolveAIOpsEngineCategory() int {

	switch executor.category {

	case Anomaly:

		return 1

	case Forecast:

		return 2

	default:

		return utils.NotAvailable

	}
}

func (executor *Executor) GetDecoder() Decoder {

	return executor.decoder
}

func (executor *Executor) GetEncoder() Encoder {

	return executor.encoder
}

func (executor *Executor) GetStringOrdinalMappings() *swiss.Map[int32, []int] {

	return executor.stringOrdinalMappings
}

func (executor *Executor) GetNumericOrdinalMappings() *swiss.Map[int32, []int] {

	return executor.numericOrdinalMappings
}

func (executor *Executor) GetErrorElementSize() int {

	return executor.errorElementSize
}

func (executor *Executor) GetErrors() []string {

	return executor.errors
}
