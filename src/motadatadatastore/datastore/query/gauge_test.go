/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-24			 <PERSON><PERSON><PERSON> <PERSON>-5459  Added Testcases for Log2, Log10 Statistical Function Support
 */

package query

import (
	"github.com/stretchr/testify/assert"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/writer"
	"motadatadatastore/utils"
	"os"
	"reflect"
	"strings"
	"testing"
	"time"
)

/*
	Gauge All Monitors Scalar metric
*/

//Gauge Average All Monitors

func TestGaugeAvgScalarMetricSystemCPUPercentLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[AvgSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeAvgScalarMetricSystemCPUPercentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	queryContext := utils.MotadataMap{}

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[AvgSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeAvgScalarMetricSystemCPUPercentLastToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	queryContext := utils.MotadataMap{}

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[AvgSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeAvgScalarMetricSystemCPUPercentLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	queryContext := utils.MotadataMap{}

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[AvgSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeAvgScalarMetricSystemCPUPercentThisWeek(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	queryContext := utils.MotadataMap{}

	timeline := utils.GetTimeline(ThisWeek)

	clickHouseQuery := "select avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[AvgSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeAvgScalarMetricSystemCPUPercentLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	queryContext := utils.MotadataMap{}

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\" from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[AvgSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

// Gauge Minimum All Monitors

func TestGaugeMinScalarMetricSystemCPUPercentLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select min(system_cpu_percent) as \"" + MinSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MinSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMinScalarMetricSystemCPUPercentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select min(system_cpu_percent) as \"" + MinSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MinSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMinScalarMetricSystemCPUPercentToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select min(system_cpu_percent) as \"" + MinSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MinSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMinScalarMetricSystemCPUPercentLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select min(system_cpu_percent) as \"" + MinSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MinSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMinScalarMetricSystemCPUPercentLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select min(system_cpu_percent) as \"" + MinSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MinSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMinScalarMetricSystemCPUPercentLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select min(system_cpu_percent) as \"" + MinSystemCPUPercent + "\" from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MinSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

// Gauge Maximum All Monitors

func TestGaugeMaxScalarMetricSystemCPUPercentLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select max(system_cpu_percent) as \"" + MaxSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MaxSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMaxScalarMetricSystemCPUPercentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select max(system_cpu_percent) as \"" + MaxSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MaxSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMaxScalarMetricSystemCPUPercentToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select max(system_cpu_percent) as \"" + MaxSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MaxSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMaxScalarMetricSystemCPUPercentLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select max(system_cpu_percent) as \"" + MaxSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MaxSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMaxScalarMetricSystemCPUPercentThisMonth(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(ThisMonth)

	clickHouseQuery := "select max(system_cpu_percent) as \"" + MaxSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MaxSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMaxScalarMetricSystemCPUPercentLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select max(system_cpu_percent) as \"" + MaxSystemCPUPercent + "\" from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable[MaxSystemCPUPercent][0].(float64)

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

// Gauge Sum All Monitors

func TestGaugeSumScalarMetricSystemCPUPercentLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select sum(system_cpu_percent) as \"" + SumSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[SumSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeSumScalarMetricSystemCPUPercentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select sum(system_cpu_percent) as \"" + SumSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[SumSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeSumScalarMetricSystemCPUPercentToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select sum(system_cpu_percent) as \"" + SumSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[SumSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeSumScalarMetricSystemCPUPercentLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select sum(system_cpu_percent) as \"" + SumSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[SumSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeSumScalarMetricSystemCPUPercentLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select sum(system_cpu_percent) as \"" + SumSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[SumSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeSumScalarMetricSystemCPUPercentLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select sum(system_cpu_percent) as \"" + SumSystemCPUPercent + "\" from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[SumSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

// Gauge Count All Monitors

func TestGaugeCountScalarMetricSystemCPUPercentLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select count(system_cpu_percent) as \"" + CountSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[CountSystemCPUPercent][0].(int64)

	assertions.Equal(int64(clickHouseValue), motadataValue)
}

func TestGaugeCountScalarMetricSystemCPUPercentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select count(system_cpu_percent) as \"" + CountSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[CountSystemCPUPercent][0].(int64)

	assertions.Equal(int64(clickHouseValue), motadataValue)
}

func TestGaugeCountScalarMetricSystemCPUPercentToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select count(system_cpu_percent) as \"" + CountSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[CountSystemCPUPercent][0].(int64)

	assertions.Equal(int64(clickHouseValue), motadataValue)

}

func TestGaugeCountScalarMetricSystemCPUPercentLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select count(system_cpu_percent) as \"" + CountSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[CountSystemCPUPercent][0].(int64)

	assertions.Equal(int64(clickHouseValue), motadataValue)
}

func TestGaugeCountScalarMetricSystemCPUPercentLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select count(system_cpu_percent) as \"" + CountSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[CountSystemCPUPercent][0].(int64)

	assertions.Equal(int64(clickHouseValue), motadataValue)
}

func TestGaugeCountScalarMetricSystemCPUPercentLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select count(system_cpu_percent) as \"" + CountSystemCPUPercent + "\" from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[CountSystemCPUPercent][0].(int64)

	assertions.Equal(int64(clickHouseValue), motadataValue)
}

// Gauge Search By Group Name Instance Metrics AVERAGE

func TestGaugeAvgInstanceMetricInterfaceINPacketsLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select avg(in_packets) as \" " + AvgInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select avg(in_packets) as \" " + AvgInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)
	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select avg(in_packets) as \" " + AvgInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsThisWeek(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(ThisWeek)

	clickHouseQuery := "select avg(in_packets) as \" " + AvgInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select avg(in_packets) as \" " + AvgInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select avg(in_packets) as \" " + AvgInterfaceINPackets + "\" from instance where group = 'network' and timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

// Gauge Search By Group Name Instance Metrics MINIMUM

func TestGaugeMinInstanceMetricInterfaceINPacketsLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select min(in_packets) as \" " + MinInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MinInterfaceINPackets][0].(int64))
}

func TestGaugeMinInstanceMetricInterfaceINPacketsLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select min(in_packets) as \" " + MinInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MinInterfaceINPackets][0].(int64))
}

func TestGaugeMinInstanceMetricInterfaceINPacketsToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select min(in_packets) as \" " + MinInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MinInterfaceINPackets][0].(int64))

}

func TestGaugeMinInstanceMetricInterfaceINPacketsLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select min(in_packets) as \" " + MinInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MinInterfaceINPackets][0].(int64))
}

func TestGaugeMinInstanceMetricInterfaceINPacketsLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select min(in_packets) as \" " + MinInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MinInterfaceINPackets][0].(int64))
}

func TestGaugeMinInstanceMetricInterfaceINPacketsLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select min(in_packets) as \" " + MinInterfaceINPackets + "\" from instance where group = 'network' and timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MinInterfaceINPackets][0].(int64))
}

// Gauge Search By Group Name Instance Metrics MAXIMUM

func TestGaugeMaxInstanceMetricInterfaceINPacketsLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select max(in_packets) as \"" + MaxInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MaxInterfaceINPackets][0].(int64))
}

func TestGaugeMaxInstanceMetricInterfaceINPacketsLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select max(in_packets) as \"" + MaxInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MaxInterfaceINPackets][0].(int64))
}

func TestGaugeMaxInstanceMetricInterfaceINPacketsToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select max(in_packets) as \" " + MaxInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MaxInterfaceINPackets][0].(int64))
}

func TestGaugeMaxInstanceMetricInterfaceINPacketsLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select max(in_packets) as \" " + MaxInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MaxInterfaceINPackets][0].(int64))
}

func TestGaugeMaxInstanceMetricInterfaceINPacketsLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select max(in_packets) as \"" + MaxInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MaxInterfaceINPackets][0].(int64))
}

func TestGaugeMaxInstanceMetricInterfaceINPacketsLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select max(in_packets) as \" " + MaxInterfaceINPackets + "\" from instance where group = 'network' and timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MaxInterfaceINPackets][0].(int64))
}

// Gauge Search By Group Name Instance Metrics COUNT

func TestGaugeCountInstanceMetricInterfaceINPacketsLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select count(in_packets) as \" " + CountInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := uint64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(int64(clickHouseValue), motadataDBTable[CountInterfaceINPackets][0].(int64))
}

func TestGaugeCountInstanceMetricInterfaceINPacketsLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select count(in_packets) as \" " + CountInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := uint64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(int64(clickHouseValue), motadataDBTable[CountInterfaceINPackets][0].(int64))
}

func TestGaugeCountInstanceMetricInterfaceINPacketsToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select count(in_packets) as \" " + CountInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := uint64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(int64(clickHouseValue), motadataDBTable[CountInterfaceINPackets][0].(int64))
}

func TestGaugeCountInstanceMetricInterfaceINPacketsLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select count(in_packets) as \" " + CountInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := uint64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(int64(clickHouseValue), motadataDBTable[CountInterfaceINPackets][0].(int64))
}

func TestGaugeCountInstanceMetricInterfaceINPacketsLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)
}

func TestGaugeCountInstanceMetricInterfaceINPacketsLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

}

//Gauge Last all datatype

func TestGaugeLastScalarMetricVlanPortsLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	//populateDatabases(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select vlan_ports as \" " + LastVLANPorts + "\" from scalar where monitor_id=1 and  timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-scalar-metric-vlanports.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(clickHouseValue, motadataDBTable[LastVLANPorts][0].(int64))
}

func TestGaugeLastScalarMetricInterfacesLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	//populateDatabases(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select interfaces as \" " + LastInterfaces + "\" from scalar where monitor_id=1 and  timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-scalar-metric-interfaces.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(clickHouseValue, motadataDBTable[LastInterfaces][0].(int64))
}

func TestGaugeLastScalarMetricSystemMemoryCommittedBytesLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	//populateDatabases(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select system_memory_committed_bytes as \" " + LastSystemMemoryCommittedBytes + "\" from scalar where monitor_id =1 and timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-scalar-metric-systemmemorycommittedbytes.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(clickHouseValue, motadataDBTable[LastSystemMemoryCommittedBytes][0].(int64))
}

func TestGaugeLastScalarMetricSystemOSNameLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	//populateDatabases(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select system_os_name as \" " + LastSystemOSName + "\" from scalar where monitor_id =1 and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-scalar-metric-systemosname.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue string

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(clickHouseValue, motadataDBTable[LastSystemOSName][0].(string))
}

func TestGaugeLastScalarMetricSystemCPUPercentLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select system_cpu_percent as \" " + LastSystemCPUPercent + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 10"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	/*
		In last query the motadata datastore returns the last value of any random monitor it picks.
		so to compare it with clickhouse we need to check for each monitor's last value
	*/

	var clickHouseValues []float64

	for rows.Next() {

		var value float64

		err = rows.Scan(&value)

		clickHouseValues = append(clickHouseValues, value)

	}

	assertions.Nil(err)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	contains := false

	for index := 0; index < 10; index++ {

		if clickHouseValues[index] == motadataDBTable[LastSystemCPUPercent][0].(float64) {

			contains = true

			break
		}
	}

	assertions.True(contains, "clickhouse value not equal to motadataDb value")

}

//Gauge Filter Type 3

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select avg(in_packets) as \"" + AvgInterfaceINPackets + "\" from instance where ((admin_status='up' and operational_status like  'main%' and last_change= '1 day' ) or (admin_status like 'u%' or  operational_status in ('unknown','maintenance'))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusLast1MonthType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select avg(in_packets) as \"" + AvgInterfaceINPackets + "\" from instance where not admin_status='up' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange-type1.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast1MonthInvalidFilterType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange-invalidfilter.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.True(strings.Contains(errs, "condition length is greater than 3"))

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast1MonthInvalidFilterType2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange-invalidfilter-type2.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.True(strings.Contains(errs, "invalid data filter"))

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select avg(in_packets) as \"" + AvgInterfaceINPackets + "\" from instance where ((admin_status='up' and operational_status like  'main%' and last_change= '1 day' ) or (admin_status like 'u%' or  operational_status in ('unknown','maintenance'))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select avg(in_packets) as \"" + AvgInterfaceINPackets + "\" from instance where ((admin_status='up' and operational_status like  'main%' and last_change= '1 day' ) or (admin_status like 'u%' or  operational_status in ('unknown','maintenance'))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select avg(in_packets) as \"" + AvgInterfaceINPackets + "\" from instance where ((admin_status='up' and operational_status like  'main%' and last_change= '1 day' ) or (admin_status like 'u%' or  operational_status in ('unknown','maintenance'))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select avg(in_packets) as \"" + AvgInterfaceINPackets + "\" from instance where ((admin_status='up' and operational_status like  'main%' and last_change= '1 day' ) or (admin_status like 'u%' or  operational_status in ('unknown','maintenance'))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select avg(in_packets) as \"" + AvgInterfaceINPackets + "\" from instance where ((admin_status='up' and operational_status like  'main%' and last_change= '1 day' ) or (admin_status like 'u%' or  operational_status in ('unknown','maintenance'))) and timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

//Gauge Filter Type 4

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where (((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d%' or operational_status = 'unknown' or admin_status like '%n' )))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}

}

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where ((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d%' or operational_status = 'unknown' or admin_status like '%n' ))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}
}

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where ((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d' or operational_status = 'unknown' or admin_status like '%d' ))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}
}

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where ((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d' or operational_status = 'unknown' or admin_status like '%d' ))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}
}

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where ((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d' or operational_status = 'unknown' or admin_status like '%d' ))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}
}

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast5Minutes(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where ((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d' or operational_status = 'unknown' or admin_status like '%d' ))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}
}

//Gauge filter type 5

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeInterfaceINPacketsLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where (((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d%' or operational_status = 'unknown' or admin_status like '%n' or in_packets in [1234,5555555,989876])))) and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}
}

/*----------------------------------------------------Gauge Scenarios end -----------------------------------------------------------*/

func TestGaugeAvgScalarMetricDummyNumericColumnLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(CustomDB)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {

		panic(err)
	}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-dummynumericcolumn-datatypemigration.json")

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select avg(dummy_numeric_column) as \"" + AvgDummyNumericColumn + "\" from dummyTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var value float64

	err = rows.Scan(&value)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertValueHavingErrorTolerance(codec.ToFixed(value), motadataDBTable[AvgDummyNumericColumn][0].(float64), assertions)

}

func TestGaugeMinScalarMetricDummyNumericColumnLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(CustomDB)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {

		panic(err)
	}

	bytes, _ := os.ReadFile(testDir + "gauge-min-scalar-metric-dummynumericcolumn-datatypemigration.json")

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select min(dummy_numeric_column) as \"" + MinDummyNumericColumn + "\" from dummyTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var value float64

	err = rows.Scan(&value)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertValueHavingErrorTolerance(codec.ToFixed(value), motadataDBTable[MinDummyNumericColumn][0].(float64), assertions)

}

func TestGaugeMaxScalarMetricDummyNumericColumnLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(CustomDB)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {

		panic(err)
	}

	bytes, _ := os.ReadFile(testDir + "gauge-max-scalar-metric-dummynumericcolumn-datatypemigration.json")

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select max(dummy_numeric_column) as \"" + MaxDummyNumericColumn + "\" from dummyTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var value float64

	err = rows.Scan(&value)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertValueHavingErrorTolerance(codec.ToFixed(value), motadataDBTable[MaxDummyNumericColumn][0].(float64), assertions)

}

func TestGaugeSumScalarMetricDummyNumericColumnLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(CustomDB)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {

		panic(err)
	}

	bytes, _ := os.ReadFile(testDir + "gauge-sum-scalar-metric-dummynumericcolumn-datatypemigration.json")

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select sum(dummy_numeric_column) as \"" + SumDummyNumericColumn + "\" from dummyTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var value float64

	err = rows.Scan(&value)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertValueHavingErrorTolerance(codec.ToFixed(value), motadataDBTable[SumDummyNumericColumn][0].(float64), assertions)

}

func TestGaugeCountScalarMetricDummyNumericColumnLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(CustomDB)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	if err != nil {

		panic(err)
	}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-dummynumericcolumn-datatypemigration.json")

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select count(dummy_numeric_column) as \"" + CountDummyNumericColumn + "\" from dummyTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var value uint64

	err = rows.Scan(&value)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	assertions.Equal(int64(value), motadataDBTable[CountDummyNumericColumn][0].(int64))

}

/*-------------------------worker event datatype migration-----------------------------------------------*/

// Invalid keys :- Data not found for each and every key -ve scenario
func TestGaugeInvalidKeys(t *testing.T) {

	defer cache.Clear()

	assertions := assert.New(t)

	timeline := utils.GetTimeline(Last1Month)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-invalid-keys.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, _ := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

}

// 1 day float 1 day int

func TestGaugeLastScalarMetricDummyINTFLOATColumnCustomLast2Days(t *testing.T) {

	defer cache.Clear()

	// we can't assert values in the particular scenario as in gauge we get random monitor last value, and we don't get monitorId, so we can't compare the same

	for i := 0; i < 10; i++ {

		connection, ctx, err := writer.GetClickhouseDBConnection(CustomDB)

		if err != nil {

			panic(err)
		}

		bytes, _ := os.ReadFile(testDir + "gauge-last-dummyintfloatcustom.json")

		timeline := utils.GetTimeline(Last2Days)

		clickHouseQuery := "select monitor_id, argMax(dummy_intFloat_column_custom,timestamp) from dummyIntFloatTableCustom where monitor_id=1 and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by monitor_id "

		assertions := assert.New(t)

		queryContext := utils.MotadataMap{}

		queryContext = utils.UnmarshalJson(bytes, queryContext)

		queryContext[VisualizationTimeline] = timeline

		startTime := time.Now().UnixMilli()

		motadataDBTable, errs := notifyExecutor(queryContext)

		assertions.Equal(utils.Empty, errs)

		endTime := time.Now().UnixMilli()

		motadataDBQueryTime := endTime - startTime

		assertions.NotNil(motadataDBTable)

		startTime = time.Now().UnixMilli()

		rows, err := connection.Query(ctx, clickHouseQuery)

		var clickHouseResult float64

		for rows.Next() {

			var dummyColumn float64

			var monitorId int32

			err = rows.Scan(&monitorId, &dummyColumn)

			clickHouseResult = dummyColumn

		}

		endTime = time.Now().UnixMilli()

		clickHouseQueryTime := endTime - startTime

		publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

		utils.CloseClickHouseDBConnection(connection, ctx)

		assertions.NotNil(clickHouseResult)

		assertions.NotNil(motadataDBTable)
	}

}

// 1 day int 1 day string

func TestGaugeLastScalarMetricDummyINTStringColumnCustomLast2Days(t *testing.T) {

	defer cache.Clear()

	// we can't assert values in the particular scenario as in gauge we get random monitor last value, and we don't get monitorId, so we can't compare the same

	for i := 0; i < 5; i++ {

		connection, ctx, err := writer.GetClickhouseDBConnection(CustomDB)

		if err != nil {

			panic(err)
		}

		bytes, _ := os.ReadFile(testDir + "gauge-last-dummyintstring.json")

		timeline := utils.GetTimeline(Last2Days)

		clickHouseQuery := "select monitor_id, argMax(dummy_intString_column,timestamp) from dummyIntStringTable where monitor_id in (1,2) and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by monitor_id "

		assertions := assert.New(t)

		queryContext := utils.MotadataMap{}

		queryContext = utils.UnmarshalJson(bytes, queryContext)

		queryContext[VisualizationTimeline] = timeline

		startTime := time.Now().UnixMilli()

		motadataDBTable, errs := notifyExecutor(queryContext)

		assertions.Equal(utils.Empty, errs)

		endTime := time.Now().UnixMilli()

		motadataDBQueryTime := endTime - startTime

		assertions.NotNil(motadataDBTable)

		startTime = time.Now().UnixMilli()

		rows, err := connection.Query(ctx, clickHouseQuery)

		var clickHouseResult float64

		for rows.Next() {

			var dummyColumn float64

			var monitorId int32

			err = rows.Scan(&monitorId, &dummyColumn)

			clickHouseResult = dummyColumn

		}

		endTime = time.Now().UnixMilli()

		clickHouseQueryTime := endTime - startTime

		publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

		utils.CloseClickHouseDBConnection(connection, ctx)

		assertions.NotNil(clickHouseResult)

		assertions.NotNil(motadataDBTable)
	}

}

func TestGaugeLastInstanceMetricInterfaceINPacketsFilterByInterfaceAdminStatusLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select argMax(in_packets,timestamp) as \"" + LastInterfaceINPackets + "\" from instance where admin_status='up' and monitor_id =1 and instance= 'x' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-instance-metric-interfaceinpackets-filterby-interfaceadminstatus.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue int64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[LastInterfaceINPackets][0].(int64)

	assertions.Equal(clickHouseValue, motadataValue)
}

/*--------------------------------------Gauge Test Case Scenarios-------------------------------------------------*/

//gauge min max sum count avg int 8

func TestGaugeMinFieldLogLevelLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-loglevel.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  min(log_level) as \"" + MinLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinLogLevel][0])

}

func TestGaugeMaxFieldLogLevelLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-loglevel.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  max(log_level) as \"" + MaxLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxLogLevel][0])

}

func TestGaugeSumFieldLogLevelLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-loglevel.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  sum(log_level) as \"" + SumLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumLogLevel][0])

}

func TestGaugeCountFieldLogLevelLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-loglevel.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  count(log_level) as \"" + CountLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountLogLevel][0])

}

func TestGaugeAvgFieldLogLevelLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-loglevel.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  avg(log_level) as \"" + AvgLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgLogLevel][0])

}

//gauge min max sum count avg int 16

func TestGaugeMinFieldBytesReceivedLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-bytesreceived.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(bytes_received) as \"" + MinBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinBytesReceived][0])

}

func TestGaugeMaxFieldBytesReceivedLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-bytesreceived.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(bytes_received) as \"" + MaxBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxBytesReceived][0])

}

func TestGaugeSumFieldBytesReceivedLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-bytesreceived.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(bytes_received) as \"" + SumBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumBytesReceived][0])

}

func TestGaugeCountFieldBytesReceivedLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-bytesreceived.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(bytes_received) as \"" + CountBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountBytesReceived][0])

}

func TestGaugeAvgFieldBytesReceivedLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-bytesreceived.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(bytes_received) as \"" + AvgBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgBytesReceived][0])

}

//gauge min max sum count avg int 32

func TestGaugeMinFieldProcessesLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-processes.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(processes) as \"" + MinProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinProcesses][0])

}

func TestGaugeMaxFieldProcessesLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-processes.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(processes) as \"" + MaxProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxProcesses][0])

}

func TestGaugeSumFieldProcessesLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

func TestGaugeCountFieldProcessesLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-processes.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(processes) as \"" + CountProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountProcesses][0])

}

func TestGaugeAvgFieldProcessesLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-processes.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(processes) as \"" + AvgProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgProcesses][0])

}

//gauge min max sum count avg int 64

func TestGaugeMinFieldBytesSentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-bytessent.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(bytes_sent) as \"" + MinBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinBytesSent][0])

}

func TestGaugeMaxFieldBytesSentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-bytessent.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(bytes_sent) as \"" + MaxBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxBytesSent][0])

}

func TestGaugeSumFieldBytesSentLast24Hours(t *testing.T) {

	defer cache.Clear()

	//cannot compare with clickhouse as clickhouse doest not convert the datatype to float and value becomes negative
	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-bytessent.json")

	timeline := utils.GetTimeline(Last5Minutes)

	clickHouseQuery := "select  sum(bytes_sent) as \"" + SumBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.True(motadataDBTable[SumBytesSent][0].(float64) > 0)
}

func TestGaugeCountFieldBytesSentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-bytessent.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(bytes_sent) as \"" + CountBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountBytesSent][0])

}

func TestGaugeAvgFieldBytesSentLast24Hours(t *testing.T) {

	defer cache.Clear()

	//cannot compare with clickhouse as clickhouse doest not convert the datatype to float and value becomes negative
	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-bytessent.json")

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select  avg(bytes_sent) as \"" + AvgBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.True(motadataDBTable[AvgBytesSent][0].(float64) > 0)

}

//rolling window testcases

func TestGaugeMinFieldBytesSentLast24HoursType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-bytessent.json")

	timeline, partialTimeLineQuery := utils.GetRollingWindowTimeline(2, 9, 17, -1)

	clickHouseQuery := "select  min(bytes_sent) as \"" + MinBytesSent + "\" from logTable1 where " + partialTimeLineQuery

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinBytesSent][0])

}

func TestGaugeCountFieldBytesSentLast24HoursType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-bytessent.json")

	timeline, partialTimeLineQuery := utils.GetRollingWindowTimeline(2, 9, 17, -1)

	clickHouseQuery := "select  count(bytes_sent) as \"" + CountBytesSent + "\" from logTable1 where " + partialTimeLineQuery

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountBytesSent][0])

}

//rolling window with filter

func TestGaugeCountFieldLogLevelLast24HoursFilterBySystemOSType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-loglevel-filterby-systemos.json")

	timeline, partialTimeLineQuery := utils.GetRollingWindowTimeline(2, 9, 17, -1)

	clickHouseQuery := "select  count(log_level) as \"" + CountLogLevel + "\" from logTable1 where " + partialTimeLineQuery + " and system_os like '%o%' and event_source='10.20.40.140'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountLogLevel][0])

}

func TestGaugeAvgFieldLogLevelLast24HoursFilterBySystemOSType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-loglevel-filterby-systemos.json")

	timeline, partialTimeLineQuery := utils.GetRollingWindowTimeline(2, 9, 17, -1)

	clickHouseQuery := "select  avg(log_level) as \"" + AvgLogLevel + "\" from logTable1 where " + partialTimeLineQuery + " and system_os in ('linux','macos')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgLogLevel][0])

}

//rolling window vertical combinations

func TestGaugeAvgScalarMetricSystemCPUPercentLast1MonthType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline, partialTimelineQuery := utils.GetRollingWindowTimeline(2, 9, 15, -1)

	clickHouseQuery := "select avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\" from scalar where " + partialTimelineQuery

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	clickHouseValue = codec.ToFixed(clickHouseValue)

	assertions.Nil(err)

	motadataValue := codec.ToFixed(motadataDBTable[AvgSystemCPUPercent][0].(float64))

	assertValueHavingErrorTolerance(clickHouseValue, motadataValue, assertions)

}

func TestGaugeMinInstanceMetricInterfaceINPacketsLast6HoursType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline, partialTimelineQuery := utils.GetRollingWindowTimeline(2, 9, 15, -1)

	clickHouseQuery := "select min(in_packets) as \" " + MinInterfaceINPackets + "\" from instance where group = 'network' and " + partialTimelineQuery

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-min-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseValue := int64(0)

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Equal(clickHouseValue, motadataDBTable[MinInterfaceINPackets][0].(int64))
}

//group by interface with filter

func TestGaugeAvgInstanceMetricInterfaceOUTPacketsFilterByInterfaceAdminStatusInterfaceOperationalStatusInterfaceLastChangeLast1MonthType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline, partialTimelineQuery := utils.GetRollingWindowTimeline(2, 9, 15, -1)

	clickHouseQuery := "select avg(out_packets) as \"" + AvgInterfaceOUTPackets + "\" from instance where (((admin_status like '%up' and operational_status like  'main%' and last_change= '1 day' ) and (not( admin_status like '%d%' or operational_status = 'unknown' or admin_status like '%n' )))) and " + partialTimelineQuery

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceoutpackets-filterby-interfaceadminstatus-interfaceoperationalstatus-interfacelastchange.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable[AvgInterfaceOUTPackets][0]

	if reflect.TypeOf(motadataValue).Name() == "float32" || reflect.TypeOf(motadataValue).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, motadataValue.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), motadataValue.(int64))
	}

}

//gauge combinations with filter int 8

func TestGaugeMinFieldLogLevelLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-loglevel-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(log_level) as \"" + MinLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os = 'linux'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinLogLevel][0])

}

func TestGaugeMaxFieldLogLevelLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-loglevel-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(log_level) as \"" + MaxLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like 'l%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxLogLevel][0])

}

func TestGaugeSumFieldLogLevelLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-loglevel-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(log_level) as \"" + SumLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%os'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumLogLevel][0])

}

func TestGaugeCountFieldLogLevelLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-loglevel-filterby-systemos.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  count(log_level) as \"" + CountLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%' and event_source='10.20.40.140'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountLogLevel][0])

}

func TestGaugeAvgFieldLogLevelLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-loglevel-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(log_level) as \"" + AvgLogLevel + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os in ('linux','macos')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgLogLevel][0])

}

//gauge combinations with filter int16

func TestGaugeMinFieldBytesReceivedLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-bytesreceived-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(bytes_received) as \"" + MinBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinBytesReceived][0])

}

func TestGaugeMaxFieldBytesReceivedLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-bytesreceived-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(bytes_received) as \"" + MaxBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxBytesReceived][0])

}

func TestGaugeSumFieldBytesReceivedLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-bytesreceived-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(bytes_received) as \"" + SumBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumBytesReceived][0])

}

func TestGaugeCountFieldBytesReceivedLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-bytesreceived-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(bytes_received) as \"" + CountBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountBytesReceived][0])

}

func TestGaugeAvgFieldBytesReceivedLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-bytesreceived-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(bytes_received) as \"" + AvgBytesReceived + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgBytesReceived][0])

}

//gauge combinations with filter int32

func TestGaugeMinFieldProcessesLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-processes-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(processes) as \"" + MinProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinProcesses][0])

}

func TestGaugeMaxFieldProcessesLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-processes-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(processes) as \"" + MaxProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxProcesses][0])

}

func TestGaugeSumFieldProcessesLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

func TestGaugeCountFieldProcessesLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-processes-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(processes) as \"" + CountProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountProcesses][0])

}

func TestGaugeAvgFieldProcessesLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-processes-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(processes) as \"" + AvgProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgProcesses][0])

}

//gauge combinations with filter int64

func TestGaugeMinFieldBytesSentLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-bytesent-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(bytes_sent) as \"" + MinBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinBytesSent][0])

}

func TestGaugeMaxFieldBytesSentLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-bytessent-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(bytes_sent) as \"" + MaxBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxBytesSent][0])

}

func TestGaugeSumFieldBytesSentLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	//cannot compare with clickhouse as clickhouse doest not convert the datatype to float and value becomes negative

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-bytessent-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(bytes_sent) as \"" + SumBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.True(motadataDBTable[SumBytesSent][0].(float64) > 0)

}

func TestGaugeCountFieldSourcePortLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-sourceport-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(source_port) as \"" + CountSourcePort + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountSourcePort][0])

}

func TestGaugeAvgFieldSourcePortLast24HoursFilterBySystemOS(t *testing.T) {

	defer cache.Clear()

	//cannot compare with clickhouse as clickhouse doest not convert the datatype to float and value becomes negative
	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-bytessent-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(bytes_sent) as \"" + AvgBytesSent + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.True(motadataDBTable[AvgBytesSent][0].(float64) > 0)

}

//gauge event policy combination

func TestGaugeMinFieldPolicyValueLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-policyvalue.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(value) as \"" + MinPolicyValue + "\" from policyTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinPolicyValue][0])

}

func TestGaugeMaxFieldPolicyValueLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-policyvalue.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(value) as \"" + MaxPolicyValue + "\" from policyTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxPolicyValue][0])

}

func TestGaugeSumFieldPolicyValueLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-policyvalue.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(value) as \"" + SumPolicyValue + "\" from policyTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumPolicyValue][0])

}

func TestGaugeCountFieldPolicyValueLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-policyvalue.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(value) as \"" + CountPolicyValue + "\" from policyTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountPolicyValue][0])

}

func TestGaugeAvgFieldPolicyValueLast1Hour(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-policyvalue.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(value) as \"" + AvgPolicyValue + "\" from policyTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgPolicyValue][0])

}

func TestGaugeAvgFieldPolicyValueLast1HourInvalidPlugin(t *testing.T) {

	defer cache.Clear()

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-policyvalue.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext.GetMapValue(VisualizationDataSources).Delete(utils.Plugins)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.True(strings.Contains(errs, "plugin is required"))

}

// different datastore type testcase for parser coverage

func TestGaugeAvgFieldCorrelatedMetricLast1Hour(t *testing.T) {

	defer cache.Clear()

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-correlatedmetric.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext.GetMapValue(VisualizationDataSources).Delete(utils.Plugins)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.True(strings.Contains(errs, "plugin is required"))

}

func TestGaugeAvgFieldStatusMetricLast1Hour(t *testing.T) {

	defer cache.Clear()

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-monitoruptime.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.True(strings.Contains(errs, "qualified key element size is zero"))

}

func TestGaugeAvgFieldPolicyResultLast1Hour(t *testing.T) {

	defer cache.Clear()

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-value-policyresult.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.True(strings.Contains(errs, "posting list lookup failed"))

}

//gauge metric policy combination

func TestGaugeMinFieldPolicyValueLast6HoursFilterByPolicySeverity(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-policyvalue-filterby-policyseverity.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(value) as \"" + MinPolicyValue + "\" from policyTable2 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and severity='down'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinPolicyValue][0])

}

func TestGaugeMaxFieldPolicyValueLast6HoursFilterByPolicySeverity(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-policyvalue-filterby-policyseverity.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(value) as \"" + MaxPolicyValue + "\" from policyTable2 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and severity='down'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxPolicyValue][0])

}

func TestGaugeSumFieldPolicyValueLast6HoursFilterByPolicySeverity(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-policyvalue-filterby-policyseverity.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(value) as \"" + SumPolicyValue + "\" from policyTable2 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and severity='down'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumPolicyValue][0])

}

func TestGaugeCountFieldPolicyValueLast6HoursFilterByPolicySeverity(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-policyvalue-filterby-policyseverity.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(value) as \"" + CountPolicyValue + "\" from policyTable2 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and severity='down'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountPolicyValue][0])

}

func TestGaugeCountFieldPolicyValueLast6HoursFilterByPolicySeverityInvalidPlugin(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-policyvalue-filterby-policyseverity.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext.GetMapValue(VisualizationDataSources).Delete(utils.Plugins)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Nil(motadataDBTable)

	assertions.True(strings.Contains(errs, "plugin is required"))

}

//Gauge flow scenario without filter

func TestGaugeMinFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-volumebytespersec.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  min(volume_bytes_per_sec) as \"" + MinVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinVolumeBytesPerSec][0])

}

func TestGaugeMaxFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-volumebytespersec.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  max(volume_bytes_per_sec) as \"" + MaxVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxVolumeBytesPerSec][0])

}

func TestGaugeSumFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-volumebytespersec-v2.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(volume_bytes_per_sec) as \"" + SumVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumVolumeBytesPerSec][0])

}

func TestGaugeCountFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-volumebytespersec-v2.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  count(volume_bytes_per_sec) as \"" + CountVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountVolumeBytesPerSec][0])

}

func TestGaugeAvgFieldVolumeBytesPerSecLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-volumebytespersec-v2.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(volume_bytes_per_sec) as \"" + AvgVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertValueHavingErrorTolerance(clickHouseValue, utils.ToFlOAT(motadataDBTable[AvgVolumeBytesPerSec][0]), assertions)

}

//Gauge flow scenario with filter

func TestGaugeMinFieldVolumeBytesPerSecLast6HoursFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-volumebytespersec-filterby-eventsource.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  min(volume_bytes_per_sec) as \"" + MinVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.140','10.20.40.141')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinVolumeBytesPerSec][0])

}

func TestGaugeMaxFieldVolumeBytesPerSecLast6HoursFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-volumebytespersec-filterby-eventsource.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  max(volume_bytes_per_sec) as \"" + MaxVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.140','10.20.40.141')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MaxVolumeBytesPerSec][0])

}

func TestGaugeSumFieldVolumeBytesPerSecLast6HoursFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-volumebytespersec-filterby-eventsource.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  sum(volume_bytes_per_sec) as \"" + SumVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.140','10.20.40.141')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumVolumeBytesPerSec][0])

}

func TestGaugeCountFieldVolumeBytesPerSecLast6HoursFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-volumebytespersec-filterby-eventsource.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  count(volume_bytes_per_sec) as \"" + CountVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.140','10.20.40.141')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[CountVolumeBytesPerSec][0])

}

func TestGaugeAvgFieldVolumeBytesPerSecLast6HoursFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-volumebytespersec-filterby-eventsource.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  avg(volume_bytes_per_sec) as \"" + AvgVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.140','10.20.40.141')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertValueHavingErrorTolerance(clickHouseValue, utils.ToFlOAT(motadataDBTable[AvgVolumeBytesPerSec][0]), assertions)

}

//gauge drilldown filter

func TestGaugeMinFieldVolumeBytesPerSecLast6HoursDrillDownFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-volumebytespersec-drilldown-filterby-eventsource.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select  min(volume_bytes_per_sec) as \"" + MinVolumeBytesPerSec + "\" from flowTable where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source in ('10.20.40.140','10.20.40.141')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[MinVolumeBytesPerSec][0])

}

func TestGaugeSumFieldProcessesLast24HoursFilterBySystemOSDrillDownFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-systemos-drilldownfilter-eventsource.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (system_os like '%o%') and (event_source in ('10.20.40.140' ,'10.20.40.141'))"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// greater than
func TestGaugeSumFieldProcessesLast24HoursFilterBySourcePortMessageDrillDownFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-sourceport-message-drilldownfilter-eventsource.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (source_port > 15) and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// less than
func TestGaugeSumFieldProcessesLast24HoursFilterBySourcePortMessageDrillDownFilterByEventSourceType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-sourceport-message-drilldownfilter-eventsource-type1.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (source_port < 85) and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// greater than equal
func TestGaugeSumFieldProcessesLast24HoursFilterBySourcePortMessageDrillDownFilterByEventSourceType2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-sourceport-message-drilldownfilter-eventsource-type2.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (source_port >= 50) and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// less than equal
func TestGaugeSumFieldProcessesLast24HoursFilterBySourcePortMessageDrillDownFilterByEventSourceType3(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-sourceport-message-drilldownfilter-eventsource-type3.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (source_port <= 50) and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// equal
func TestGaugeSumFieldProcessesLast24HoursFilterBySourcePortMessageDrillDownFilterByEventSourceType4(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-sourceport-message-drilldownfilter-eventsource-type4.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (source_port = 50) and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	if clickHouseValue > 0 {

		assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

	}

}

// in numeric value single ordinal
func TestGaugeSumFieldProcessesLast24HoursFilterBySourcePortMessageDrillDownFilterByEventSourceType5(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-sourceport-message-drilldownfilter-eventsource-type5.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (source_port in ( 50 )) and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	if clickHouseValue > 0 {

		assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

	}

}

// in numeric value multiple ordinals
func TestGaugeSumFieldProcessesLast24HoursFilterBySourcePortMessageDrillDownFilterByEventSourceType6(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-sourceport-message-drilldownfilter-eventsource-type6.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (source_port in ( 50 ,60,70,80 )) and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	if clickHouseValue > 0 {

		assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

	}

}

// contains
func TestGaugeSumFieldProcessesLast24HoursFilterBySourceOSMessageDrillDownFilterByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-systemos-message-drilldownfilter-eventsource.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (system_os like '%o%') and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// end with
func TestGaugeSumFieldProcessesLast24HoursFilterBySourceOSMessageDrillDownFilterByEventSourceType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-systemos-message-drilldownfilter-eventsource-type1.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (system_os like '%ux') and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// start with
func TestGaugeSumFieldProcessesLast24HoursFilterBySourceOSMessageDrillDownFilterByEventSourceType2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-systemos-message-drilldownfilter-eventsource-type2.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (system_os like 'li%') and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// equals
func TestGaugeSumFieldProcessesLast24HoursFilterBySourceOSMessageDrillDownFilterByEventSourceType3(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-systemos-message-drilldownfilter-eventsource-type3.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (system_os = 'linux') and message = 'message1'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

// in
func TestGaugeSumFieldProcessesLast24HoursFilterBySourceOSMessageDrillDownFilterByEventSourceType4(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-processes-filterby-systemos-message-drilldownfilter-eventsource-type4.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  sum(processes) as \"" + SumProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (system_os in ('linux','windows' ,'centos') and message = 'message1')"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable[SumProcesses][0])

}

//-------------------------------------------------metric aggregation -----------------------------------------------------------------------------

// float columns
func TestGaugeMinFieldDummyFLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()
	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-dummyfloatcolumn1.json")

	timeline := utils.GetTimeline(Last6Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyFLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyfloatcolumn1.json")

	timeline := utils.GetTimeline(Last6Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyFLOATColumn1Last1Hour(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyfloatcolumn1.json")

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyFLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyfloatcolumn1.json")

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyFLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyFLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyFLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyfloatcolumn1.json")

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	modifyEntities(queryContext)

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyFLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyFLOATColumn1])[0], assertions)

}

//int8 to float columns

func TestGaugeMinFieldDummyINT8FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint8floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT8FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint8floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT8FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint8floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT8FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint8floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT8FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT8FLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyINT8FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint8floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT8FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT8FLOATColumn1])[0], assertions)

}

//int16 to float  columns

func TestGaugeMinFieldDummyINT16FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint16floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT16FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint16floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT16FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint16floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT16FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint16floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT16FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT16FLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyINT16FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint16floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT16FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT16FLOATColumn1])[0], assertions)

}

//int32 to float columns

func TestGaugeMinFieldDummyINT32FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint32floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT32FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint32floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT32FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint32floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT32FLOATColumn1Last48Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint32floatcolumn1.json")

	timeline := utils.GetTimeline(Last48Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT32FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT32FLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyINT32FLOATColumn1Last24Hours(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint32floatcolumn1.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT32FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT32FLOATColumn1])[0], assertions)

}

//int64 to float columns

func TestGaugeMinFieldDummyINT64FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint64floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT64FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint64floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT64FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint64floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT64FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint64floatcolumn1.json")

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	if reflect.TypeOf(motadataDBAggregationView[SumDummyINT64FLOATColumn1][0]).Kind().String() == "float64" {

		assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT64FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT64FLOATColumn1])[0], assertions)

	} else {

		assertions.Equal(utils.InterfaceToINT64Values(motadataDBAggregationView[SumDummyINT64FLOATColumn1])[0], utils.InterfaceToINT64Values(motadataDBTable[SumDummyINT64FLOATColumn1])[0])
	}

}

func TestGaugeAvgFieldDummyINT64FLOATColumn1Last1Month(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint64floatcolumn1.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT64FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT64FLOATColumn1])[0], assertions)

}

// float columns with condition
func TestGaugeMinFieldDummyFLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-dummyfloatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyFLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyfloatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyFLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyfloatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Hour)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyFLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyfloatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyFLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyFLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyFLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyfloatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyFLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyFLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyFLOATColumn1])[0], assertions)

}

//int8 to float columns with condition

func TestGaugeMinFieldDummyINT8FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-dummyint8floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}
	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT8FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint8floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT8FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint8floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT8FLOATColumn1Last48HoursFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint8floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last48Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT8FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT8FLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyINT8FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint8floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT8FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT8FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT8FLOATColumn1])[0], assertions)

}

//int16 to float  columns with condition

func TestGaugeMinFieldDummyINT16FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-dummyint16floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT16FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint16floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT16FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint16floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT16FLOATColumn1Last24HoursFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint16floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT16FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT16FLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyINT16FLOATColumn1Last48HoursFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint16floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last48Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT16FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT16FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT16FLOATColumn1])[0], assertions)

}

//int32 to float columns with condition

func TestGaugeMinFieldDummyINT32FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-min-field-dummyint32floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT32FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint32floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT32FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint32floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT32FLOATColumn1Last24HoursFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint32floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT32FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT32FLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyINT32FLOATColumn1Last48HoursFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint32floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last48Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT32FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT32FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT32FLOATColumn1])[0], assertions)

}

//int64 to float columns with condition

func TestGaugeMinFieldDummyINT64FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint64floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeMaxFieldDummyINT64FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-max-field-dummyint64floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeCountFieldDummyINT64FLOATColumn1Last1MonthFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-dummyint64floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertions.Equal(motadataDBAggregationView, motadataDBTable)

}

func TestGaugeSumFieldDummyINT64FLOATColumn1Last24HoursFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-dummyint64floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[SumDummyINT64FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[SumDummyINT64FLOATColumn1])[0], assertions)

}

func TestGaugeAvgFieldDummyINT64FLOATColumn1Last24HoursFilterByInterfaceAliasName(t *testing.T) {

	defer cache.Clear()

	if !utils.Aggregation {

		t.Skip("aggregation tests are disabled")
	}
	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, true)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-dummyint64floatcolumn1-filterby-interfacealiasname.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	aggregationTimeLine := make(utils.MotadataMap)

	for key, value := range timeline {

		aggregationTimeLine[key] = value
	}

	interval := chooseAggregationInterval(timeline.GetInt64Value(FromDateTime), timeline.GetInt64Value(ToDateTime), Metric)

	timeline[FromDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(FromDateTime), int(interval))

	timeline[ToDateTime] = utils.RoundOffUnixMilliSeconds(timeline.GetInt64Value(ToDateTime), int(interval))

	timeline[utils.Duration] = (timeline.GetInt64Value(ToDateTime) - timeline.GetInt64Value(FromDateTime)) / 1000

	if timeline.GetIntValue(utils.Duration) == 0 {

		t.Skip("The qualified chosen interval rounds of to same from-time and to-time, hence cannot run this test.")
	}

	queryContext[VisualizationTimeline] = timeline

	motadataDBAggregationView, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBAggregationView)

	assertions.Empty(errs)

	datastore.UpdateVerticalAggregations(writer.DummyINT64FLOATColumn1, false)

	alterAggregationTimeline(queryContext)

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.NotNil(motadataDBTable)

	assertions.Empty(errs)

	assertValueHavingErrorTolerance(utils.InterfaceToFLOAT64Values(motadataDBAggregationView[AvgDummyINT64FLOATColumn1])[0], utils.InterfaceToFLOAT64Values(motadataDBTable[AvgDummyINT64FLOATColumn1])[0], assertions)

}

func TestGaugeAvgInstanceMetricInterfaceINPacketsLast1MonthIncremental(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select avg(in_packets) as \" " + AvgInterfaceINPackets + "\" from instance where group = 'network' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-instance-metric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	assertions.Nil(err)

	assertions.NotNil(motadataDBTable)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

func TestGaugeAvgFieldProcessesLast24HoursFilterBySystemOSIncremental(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-processes-filterby-systemos.json")

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select  avg(processes) as \"" + AvgProcesses + "\" from logTable1 where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and system_os like '%o%'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable[AvgProcesses][0])

}

// health queries

func TestGaugeCountFieldHealthLast24hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-pending-events.json")

	timeline := utils.GetTimeline(Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	alterAggregationTimeline(queryContext)

	clickHouseQuery := "select  count(pending_events) as \"pending.events^count\" from health where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable["pending.events^count"][0])

}

func TestGaugeAvgFieldHealthLast24hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-avg-field-pending-events.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	alterAggregationTimeline(queryContext)

	clickHouseQuery := "select  avg(pending_events) as \"pending.events^sum\" from health where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable["pending.events^avg"][0])

}

func TestGaugeCountFieldHealthLast24hoursV2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-count-field-entity-event-source-pending-events.json")

	timeline := utils.GetTimeline(Last24Hours)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	alterAggregationTimeline(queryContext)

	clickHouseQuery := "select  count(pending_events) as \"pending.events^count\" from health where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), motadataDBTable["pending.events^count"][0])

}

func TestGaugeSumFieldHealthLast24hoursV2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-field-pending-events.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	alterAggregationTimeline(queryContext)

	//fmt.Println(timeline, interval)

	clickHouseQuery := "select  sum(pending_events) as \"pending.events^sum\" from health where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable["pending.events^sum"][0])

}

// data filter

func TestGaugeSumWithDataFilterByEngineTypeFieldHealthLast24hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-filter-by-engine-type-health-query.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	alterAggregationTimeline(queryContext)

	//fmt.Println(timeline, interval)

	clickHouseQuery := "select  sum(pending_events) as \"pending.events^sum\" from health where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and engine_type =  'config.response.processor'"

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable["pending.events^sum"][0])

}

func TestGaugeSumWithDataFilterByEnginTypeEventSourceFieldHealthLast24hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "gauge-sum-filter-by-engine-type-event-source-health-query.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	alterAggregationTimeline(queryContext)

	clickHouseQuery := "select  sum(pending_events) as \"pending.events^sum\" from health where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and (engine_type =  'config.response.processor' or event_source = '10.20.40.141')"

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable["pending.events^sum"][0])

}

// alias test cases

func TestGaugeAvgScalarMetricSystemNetworkINBytesAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.network.in.bytes", "system.network.in.bytes.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-avg-systemnetworkinbytes-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(avg(system_network_in_bytes) , 8)  as \"" + "system.network.in.bytes.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), codec.ToINT(motadataDBTable["system.network.in.bytes.alias^avg"][0]))

}

func TestGaugeSumScalarMetricSystemNetworkINBytesAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.network.in.bytes", "system.network.in.bytes.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-sum-systemnetworkinbytes-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(sum(system_network_in_bytes) , 8)  as \"" + "system.network.in.bytes.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, codec.ToINT(motadataDBTable["system.network.in.bytes.alias^sum"][0]))

}

func TestGaugeMinScalarMetricSystemNetworkINBytesAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.network.in.bytes", "system.network.in.bytes.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-min-systemnetworkinbytes-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(min(system_network_in_bytes) , 8)  as \"" + "system.network.in.bytes.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, codec.ToINT(motadataDBTable["system.network.in.bytes.alias^min"][0]))

}

func TestGaugeMaxScalarMetricSystemNetworkINBytesAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.network.in.bytes", "system.network.in.bytes.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-max-systemnetworkinbytes-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(max(system_network_in_bytes) , 8)  as \"" + "system.network.in.bytes.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, codec.ToINT(motadataDBTable["system.network.in.bytes.alias^max"][0]))

}

func TestGaugeCountScalarMetricSystemNetworkINBytesAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.network.in.bytes", "system.network.in.bytes.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-count-systemnetworkinbytes-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  count(system_network_in_bytes)  as \"" + "system.network.in.bytes.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(int64(clickHouseValue), codec.ToINT(motadataDBTable["system.network.in.bytes.alias^count"][0]))

}

func TestGaugeLastScalarMetricSystemNetworkINBytesAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.network.in.bytes", "system.network.in.bytes.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-last-systemnetworkinbytes-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select multiply(system_network_in_bytes , 8 )  as \"" + "system.network.in.bytes.alias" + "\" from scalar where monitor_id = 1 and timestamp =  (select max(timestamp) as last_tick from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1)"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue int64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, codec.ToINT(motadataDBTable["system.network.in.bytes.alias^last"][0]))

}

func TestGaugeAvgFieldSystemCPUPercentAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.cpu.percent", "system.cpu.percent.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-avg-sytemcpupercent-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(avg(system_cpu_percent) , 8)  as \"" + "system.cpu.percent.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(utils.ToFixed(clickHouseValue), motadataDBTable["system.cpu.percent.alias^avg"][0])

}

func TestGaugeMinFieldSystemCPUPercentAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.cpu.percent", "system.cpu.percent.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-min-sytemcpupercent-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(min(system_cpu_percent) , 8)  as \"" + "system.cpu.percent.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(utils.ToFixed(clickHouseValue), motadataDBTable["system.cpu.percent.alias^min"][0])

}

func TestGaugeMaxFieldSystemCPUPercentAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.cpu.percent", "system.cpu.percent.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-max-sytemcpupercent-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(max(system_cpu_percent) , 8)  as \"" + "system.cpu.percent.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(utils.ToFixed(clickHouseValue), motadataDBTable["system.cpu.percent.alias^max"][0])

}

func TestGaugeCountFieldSystemCPUPercentAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.cpu.percent", "system.cpu.percent.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-count-sytemcpupercent-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  count(system_cpu_percent)  as \"" + "system.cpu.percent.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue uint64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(clickHouseValue, motadataDBTable["system.cpu.percent.alias^count"][0])

}

func TestGaugeSumFieldSystemCPUPercentAliasLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.cpu.percent", "system.cpu.percent.alias")

	bytes, _ := os.ReadFile(testDir + "gauge-sum-sytemcpupercent-alias.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(sum(system_cpu_percent) , 8)  as \"" + "system.cpu.percent.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.EqualValues(utils.ToFixed(clickHouseValue), motadataDBTable["system.cpu.percent.alias^sum"][0])

}

func TestGaugeCountScalarMetricSystemCPUPercentAliasLast1Month(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.cpu.percent", "system.cpu.percent.alias")

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select count(system_cpu_percent)as \"" + "system.cpu.percent.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent-alias.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := codec.ToINT(motadataDBTable["system.cpu.percent.alias^count"][0])

	assertions.Equal(int(clickHouseValue), motadataValue)
}

func TestGaugeCountScalarMetricSystemCPUPercentAliasLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("system.cpu.percent", "system.cpu.percent.alias")

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select count(system_cpu_percent)as \"" + "system.cpu.percent.alias" + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1"

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-count-scalar-metric-systemcpupercent-alias.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue uint64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := codec.ToINT(motadataDBTable["system.cpu.percent.alias^count"][0])

	assertions.Equal(int(clickHouseValue), motadataValue)
}

//alias counter testcases with datatype conversion in INT64 to float64

func TestGaugeAvgInstanceMetricSystemNetworkINBytesFilterByInterfaceLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	datastore.AddShadowCounter("interface~bytes.per.sec", "interface~bits.per.sec")

	bytes, _ := os.ReadFile(testDir + "gauge-avg-interfacebitspersec-filterby-interface.json")

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select  multiply(avg(bytes_per_sec) , 8)  as \"" + "interface~bits.per.sec" + "\" from instance where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [1] and instance = 'x'"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseValue float64

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	assertions.NotNil(clickHouseValue)

}

// compliance

func TestGaugeAvgCompliancePercentLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select avg(compliance_percent) as  \"compliance.percent^avg\" from compliance where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-avg-compliancepercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue float64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable["compliance.percent^avg"][0].(int64)

	assertions.Equal(int64(clickHouseValue), motadataValue)

}

func TestGaugeMaxCompliancePercentLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	assertions := assert.New(t)

	var err error

	timeline := utils.GetTimeline(Last24Hours)

	clickHouseQuery := "select max(compliance_percent) as  \"compliance.percent^max\"  from compliance where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-max-compliancepercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseValue int64

	rows.Next()

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	motadataValue := motadataDBTable["compliance.percent^max"][0].(int64)

	assertions.Equal(clickHouseValue, motadataValue)

}

// statistical function filterby interface
func TestGaugeLog2AvgInstanceMetricInterfaceINPacketsFilterByInterfaceNameToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select log2(avg(in_packets)) as \" " + AvgInterfaceINPackets + "\" from instance where instance = 'x' and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-log2-avg-instance-metric-interfaceinpackets-filterby-interface.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[AvgInterfaceINPackets][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

// statistical function with count aggregation without filter
func TestGaugeLog2CountMetricSystemCPUPercentToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select log2(count(system_cpu_percent)) as \" " + CountVolumeBytesPerSec + "\" from scalar where monitor_id = 1  and timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-log2-count-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[CountSystemCPUPercent][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

// statistical function with last aggregation without filter
func TestGaugeLog10LastMetricSystemCPUPercentToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select log10(system_cpu_percent) as \" " + CountVolumeBytesPerSec + "\" from scalar where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + "  and  timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " order by timestamp desc limit 1"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-log10-last-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	clickHouseValue := float64(0)

	err = rows.Scan(&clickHouseValue)

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.Nil(err)

	value := motadataDBTable[LastSystemCPUPercent][0]

	if reflect.TypeOf(value).Name() == "float32" || reflect.TypeOf(value).Name() == "float64" {

		assertValueHavingErrorTolerance(clickHouseValue, value.(float64), assertions)

	} else {

		assertions.Equal(int64(clickHouseValue), value.(int64))
	}

}

// -------------------------------------- SLO testcases -----------------------------------------

func TestGaugeLastSloGlobalAchievedPercentageLast2DaysWithEntityKeys(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-slo-global-achieved-percentage-last2days-with-entity-keys.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickHouseQuery := "select achieved_percentage from sloGlobalMetrics where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 12 AND cycle_id = '3543545' order by timestamp desc limit 1"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseLastValue float64

	err = rows.Scan(&clickHouseLastValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable["slo~achieved.percentage^last"][0]

	assertions.Equal(utils.ToFixed(clickHouseLastValue), motadataValue.(float64))

}

func TestGaugeLastSloGlobalAchievedPercentageLast2DaysWithCycleIdFilter(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-slo-global-achieved-percentage-last2days-with-cycleid-filter.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickHouseQuery := "select achieved_percentage from sloGlobalMetrics where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 12 AND cycle_id = '3543545' order by timestamp desc limit 1"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseLastValue float64

	err = rows.Scan(&clickHouseLastValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable["slo~achieved.percentage^last"][0]

	assertions.Equal(utils.ToFixed(clickHouseLastValue), motadataValue.(float64))

}

func TestGaugeLastSloGlobalErrorBudgetLeftLast2DaysWithEntityKeys(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-slo-global-error-budget-left-last2days-with-entity-keys.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickHouseQuery := "select error_budget_left from sloGlobalMetrics where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 13 AND cycle_id = '3545645' order by timestamp desc limit 1"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseLastValue float64

	err = rows.Scan(&clickHouseLastValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable["slo~error.budget.left^last"][0]

	assertions.Equal(utils.ToFixed(clickHouseLastValue), motadataValue.(float64))

}

func TestGaugeLastSloGlobalErrorBudgetLeftLast2DaysWithCycleIdFilter(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "gauge-last-slo-global-error-budget-left-last2days-with-cycleid-filter.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickHouseQuery := "select error_budget_left from sloGlobalMetrics where timestamp >=" + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 13 AND cycle_id = '3545645' order by timestamp desc limit 1"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	rows.Next()

	var clickHouseLastValue float64

	err = rows.Scan(&clickHouseLastValue)

	assertions.Nil(err)

	motadataValue := motadataDBTable["slo~error.budget.left^last"][0]

	assertions.Equal(utils.ToFixed(clickHouseLastValue), motadataValue.(float64))

}
