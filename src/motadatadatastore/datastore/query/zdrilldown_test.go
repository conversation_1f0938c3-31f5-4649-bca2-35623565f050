/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>			Motadata-5190  Migrated constants from datastore package to utils package to match SonarQube Standard
* 2025-03-05             Vedant Dokania         Motadata-5451  status flap vertical testcases
* 2025-04-02			 Vedant Dokania			Motadata-4859  Refactored Test Cases
* 2025-21-04             Vedant Dokania         Motadata-4859  Added testcases for status flap missing data in object
* 2025-05-05			 <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>-6078 passed defaultblobpool const as the function parameter in memory pool initialization
 */

package query

import (
	"bytes"
	"fmt"
	"github.com/kamstrup/intmap"
	"github.com/stretchr/testify/assert"
	"math/rand"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/writer"
	"motadatadatastore/utils"
	"os"
	"testing"
	"time"
)

func TestDrillDownScalarMetricSystemCPUPercentLastQuarter(t *testing.T) {

	defer cache.Clear()

	initDrillDownQueryEngine()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(LastQuarter)

	clickHouseQuery := "select monitor_id , (timestamp)*1000 as Timestamp, system_cpu_percent from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in (1,2,3,4,5,6,7,8,9,10) order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-systemcpupercent-missingcolumn.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	//assertions.Contains(motadataDBTable, "system.cpu.percent1^value")

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	monitors := 10
	clickHouseTicks := make([][]int64, monitors)

	clickHouseValues := make([][]float64, monitors)

	for index := range clickHouseTicks {

		clickHouseTicks[index] = make([]int64, 0)

		clickHouseValues[index] = make([]float64, 0)
	}

	for rows.Next() {

		var monitor int32

		var tick int64

		var cpuValue float64

		err = rows.Scan(&monitor, &tick, &cpuValue)

		clickHouseValues[monitor-1] = append(clickHouseValues[monitor-1], codec.ToFixed(cpuValue))

		clickHouseTicks[monitor-1] = append(clickHouseTicks[monitor-1], tick)

	}

	motadataDBTicks := make([][]int64, monitors)

	motadataDBValues := make([][]float64, monitors)

	for index := range motadataDBTicks {

		motadataDBTicks[index] = make([]int64, 0)

		motadataDBValues[index] = make([]float64, 0)

	}

	for index, group := range motadataDBTable[utils.ObjectId] {

		motadataDBTicks[codec.ToINT(group)-1] = append(motadataDBTicks[codec.ToINT(group)-1], writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"])[index])

		motadataDBValues[codec.ToINT(group)-1] = append(motadataDBValues[codec.ToINT(group)-1], utils.InterfaceToFLOAT64Values(motadataDBTable["system.cpu.percent^value"])[index])
	}

	for index := range motadataDBTicks {

		assertions.EqualValues(clickHouseTicks[index], motadataDBTicks[index])

		assertValuesHavingErrorTolerance(clickHouseValues[index], motadataDBValues[index], assertions)
	}
}

// Drill down instance metric with single instance counter and with `entity keys present
func TestDrillDownInstanceMetricInterfaceINPacketsLast2Days(t *testing.T) {

	defer cache.Clear()

	initDrillDownQueryEngine()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, instance , in_packets from instance where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-instancemetric-interfaceinpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	var clickHouseInstances []string

	for rows.Next() {

		var tick int64

		var instance string

		var value int64

		err = rows.Scan(&tick, &instance, &value)

		clickHouseValues = append(clickHouseValues, value)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseInstances = append(clickHouseInstances, instance)

	}

	utils.SortStringValues(clickHouseInstances)

	utils.SortINT64Values(clickHouseValues)

	motadataDBInstances := utils.ToStringList(motadataDBTable["interface"])

	utils.SortStringValues(motadataDBInstances)

	motadataDBValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~in.packets^value"])

	utils.SortINT64Values(motadataDBValues)

	assertions.EqualValues(clickHouseValues, motadataDBValues)

	assertions.EqualValues(clickHouseInstances, motadataDBInstances)

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

}

// Drill down instance metric with single instance counter and with `entity keys present
func TestDrillDownInstanceMetricInterfaceINPacketsLast2DaysMissingEntityKeys(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, instance , in_packets from instance where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-instancemetric-interfaceinpackets-without-entitykeys.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	var clickHouseInstances []string

	for rows.Next() {

		var tick int64

		var instance string

		var value int64

		err = rows.Scan(&tick, &instance, &value)

		clickHouseValues = append(clickHouseValues, value)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseInstances = append(clickHouseInstances, instance)

	}

	utils.SortStringValues(clickHouseInstances)

	utils.SortINT64Values(clickHouseValues)

	motadataDBInstances := utils.ToStringList(motadataDBTable["interface"])

	utils.SortStringValues(motadataDBInstances)

	motadataDBValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~in.packets^value"])

	utils.SortINT64Values(motadataDBValues)

	assertions.EqualValues(clickHouseValues, motadataDBValues)

	assertions.EqualValues(clickHouseInstances, motadataDBInstances)

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

}

// Drill down instance metric with multiple instance counter and with `entity keys present
func TestDrillDownInstanceMetricInterfaceINPacketsInterfaceOUTPacketsLastMonth(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, instance , in_packets , out_packets from instance where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-instancemetric-interfaceinpackets-interfaceoutpackets.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues [][]int64

	var clickHouseInstances []string

	clickHouseGroups := make(map[string][]int64)

	for rows.Next() {

		var tick int64

		var instance string

		var inPackets, outPackets int64

		err = rows.Scan(&tick, &instance, &inPackets, &outPackets)

		clickHouseValues = append(clickHouseValues, []int64{inPackets, outPackets})

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseInstances = append(clickHouseInstances, instance)

		clickHouseGroups[utils.INT64ToStringValue(tick)+utils.GroupSeparator+instance] = []int64{inPackets, outPackets}

	}

	motadataDBInstances := utils.ToStringList(motadataDBTable["instance"])

	motadataDBTicks := writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"])

	motadataDBINPacketsValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~in.packets^value"])

	motadataDBOUTPacketsValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~out.packets^value"])

	motadataDBGroups := make(map[string][]int64)

	for index, instance := range motadataDBInstances {

		motadataDBGroups[utils.INT64ToStringValue(motadataDBTicks[index])+utils.GroupSeparator+instance] = []int64{motadataDBINPacketsValues[index], motadataDBOUTPacketsValues[index]}

	}

	for group := range motadataDBGroups {

		assertions.EqualValues(clickHouseGroups[group], motadataDBGroups[group])
	}

}

// Drill down instance metric with multiple instance counter and with `entity keys present missing column
func TestDrillDownInstanceMetricInterfaceINPacketsInterfaceOUTPacketsMissingColumnLastMonth(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, instance , in_packets , out_packets from instance where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-instancemetric-interfaceinpackets-interfaceoutpackets-missingcolumn.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues [][]int64

	var clickHouseInstances []string

	clickHouseGroups := make(map[string][]int64)

	for rows.Next() {

		var tick int64

		var instance string

		var inPackets, outPackets int64

		err = rows.Scan(&tick, &instance, &inPackets, &outPackets)

		clickHouseValues = append(clickHouseValues, []int64{inPackets, outPackets})

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseInstances = append(clickHouseInstances, instance)

		clickHouseGroups[utils.INT64ToStringValue(tick)+utils.GroupSeparator+instance] = []int64{inPackets, outPackets}

	}

	motadataDBInstances := utils.ToStringList(motadataDBTable["instance"])

	motadataDBTicks := writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"])

	motadataDBINPacketsValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~in.packets^value"])

	motadataDBOUTPacketsValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~out.packets^value"])

	assertions.Contains(motadataDBTable, "interface~missing.column^value")

	motadataDBGroups := make(map[string][]int64)

	for index, instance := range motadataDBInstances {

		motadataDBGroups[utils.INT64ToStringValue(motadataDBTicks[index])+utils.GroupSeparator+instance] = []int64{motadataDBINPacketsValues[index], motadataDBOUTPacketsValues[index]}

	}

	for group := range motadataDBGroups {

		assertions.EqualValues(clickHouseGroups[group], motadataDBGroups[group])
	}

}

// Drill down instance metric with multiple instance counter and with `entity keys absent
func TestDrillDownInstanceMetricInterfaceINPacketsInterfaceOUTPacketsLastQuarterMissingEntityKeys(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(LastQuarter)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, instance , in_packets , out_packets from instance where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-drilldown-instancemetric-interfaceinpackets-interfaceoutpackets-missing-entitykeys.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues [][]int64

	var clickHouseInstances []string

	clickHouseGroups := make(map[string][]int64)

	for rows.Next() {

		var tick int64

		var instance string

		var inPackets, outPackets int64

		err = rows.Scan(&tick, &instance, &inPackets, &outPackets)

		clickHouseValues = append(clickHouseValues, []int64{inPackets, outPackets})

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseInstances = append(clickHouseInstances, instance)

		clickHouseGroups[utils.INT64ToStringValue(tick)+utils.GroupSeparator+instance] = []int64{inPackets, outPackets}

	}

	motadataDBInstances := utils.ToStringList(motadataDBTable["instance"])

	motadataDBTicks := writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"])

	motadataDBINPacketsValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~in.packets^value"])

	motadataDBOUTPacketsValues := writer.INT64InterfaceToINT64Values(motadataDBTable["interface~out.packets^value"])

	motadataDBGroups := make(map[string][]int64)

	for index, instance := range motadataDBInstances {

		motadataDBGroups[utils.INT64ToStringValue(motadataDBTicks[index])+utils.GroupSeparator+instance] = []int64{motadataDBINPacketsValues[index], motadataDBOUTPacketsValues[index]}

	}

	for group := range motadataDBGroups {

		assertions.EqualValues(clickHouseGroups[group], motadataDBGroups[group])
	}

}

//status flap vertical instance

func TestDrillDownStatusFlapInstanceLast24Hours(t *testing.T) {

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-vertical.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _1HourGranularity

	clickHouseQuery := "select monitor_id, instance ,duration,status, timestamp*1000 as Timestamp from statusTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [0,1,2,3,4,5,6,7,8,9] order by timestamp "

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDurations []int64

	var clickHouseStatues []string

	var clickHouseInstances []string

	var clickHouseObjects []int64

	var clickHouseTicks []int64

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		var instance string

		err = rows.Scan(&monitor, &instance, &duration, &status, &tick)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseObjects = append(clickHouseObjects, int64(monitor))

		clickHouseDurations = append(clickHouseDurations, int64(duration))

		clickHouseStatues = append(clickHouseStatues, status)

		clickHouseInstances = append(clickHouseInstances, instance)

	}

	clickHouseValue = prepareClickhouseStatusFlapResult(clickHouseDurations, clickHouseObjects, clickHouseTicks, clickHouseStatues, clickHouseInstances, nil)

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["interface~duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["interface~status.flap.history^value"])

	motadataDBInstances := utils.ToStringList(motadataDBTable["interface"])

	motadataDBMonitors := utils.ToStringList(motadataDBTable[utils.ObjectId])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+motadataDBMonitors[index]+utils.GroupSeparator+motadataDBInstances[index]] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)

		} else {

			assertions.Fail(fmt.Sprintf("key mismatched %v key", key))
		}
	}
}

func TestDrillDownStatusFlapLast1MonthInstanceFilterByStatus(t *testing.T) {

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-vertical-filterby-status.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _1HourGranularity

	filterResult := []string{datastore.Up, datastore.Down}

	clickHouseQuery := "select monitor_id, instance ,duration,status, timestamp*1000 as Timestamp from statusTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [0,1,2,3,4,5,6,7,8,9] order by timestamp "

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDurations []int64

	var clickHouseStatues []string

	var clickHouseInstances []string

	var clickHouseObjects []int64

	var clickHouseTicks []int64

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		var instance string

		err = rows.Scan(&monitor, &instance, &duration, &status, &tick)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseObjects = append(clickHouseObjects, int64(monitor))

		clickHouseDurations = append(clickHouseDurations, int64(duration))

		clickHouseStatues = append(clickHouseStatues, status)

		clickHouseInstances = append(clickHouseInstances, instance)

	}

	clickHouseValue = prepareClickhouseStatusFlapResult(clickHouseDurations, clickHouseObjects, clickHouseTicks, clickHouseStatues, clickHouseInstances, filterResult)

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["interface~duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["interface~status.flap.history^value"])

	motadataDBInstances := utils.ToStringList(motadataDBTable["interface"])

	motadataDBMonitors := utils.ToStringList(motadataDBTable[utils.ObjectId])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+motadataDBMonitors[index]+utils.GroupSeparator+motadataDBInstances[index]] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)

		} else {

			assertions.Fail(fmt.Sprintf("key mismatched %v key", key))
		}
	}
}

// status flap vertical scalar

func TestDrillDownStatusFlapLast2Days(t *testing.T) {

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-scalar-vertical.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _1HourGranularity

	clickHouseQuery := "select monitor_id,duration,status, timestamp*1000 as Timestamp from statusTableMetric where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [0,1,2,3,4,5,6,7,8,9] order by timestamp "

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDurations []int64

	var clickHouseStatues []string

	var clickHouseObjects []int64

	var clickHouseTicks []int64

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		err = rows.Scan(&monitor, &duration, &status, &tick)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseObjects = append(clickHouseObjects, int64(monitor))

		clickHouseDurations = append(clickHouseDurations, int64(duration))

		clickHouseStatues = append(clickHouseStatues, status)

	}

	clickHouseValue = prepareClickhouseStatusFlapResult(clickHouseDurations, clickHouseObjects, clickHouseTicks, clickHouseStatues, nil, nil)

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["status.flap.history^value"])

	motadataDBMonitors := utils.ToStringList(motadataDBTable[utils.ObjectId])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+motadataDBMonitors[index]] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)

		} else {

			assertions.Fail(fmt.Sprintf("key mismatched %v key", key))
		}
	}
}

func TestDrillDownStatusFlapLast6HoursFilterByStatus(t *testing.T) {

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last6Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-scalar-vertical-filterby-status.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _1HourGranularity

	filterResult := []string{datastore.Up, datastore.Down}

	clickHouseQuery := "select monitor_id,duration,status, timestamp*1000 as Timestamp from statusTableMetric where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [0,1,2,3,4,5,6,7,8,9] order by timestamp "

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDurations []int64

	var clickHouseStatues []string

	var clickHouseObjects []int64

	var clickHouseTicks []int64

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		err = rows.Scan(&monitor, &duration, &status, &tick)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseObjects = append(clickHouseObjects, int64(monitor))

		clickHouseDurations = append(clickHouseDurations, int64(duration))

		clickHouseStatues = append(clickHouseStatues, status)

	}

	clickHouseValue = prepareClickhouseStatusFlapResult(clickHouseDurations, clickHouseObjects, clickHouseTicks, clickHouseStatues, nil, filterResult)

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["status.flap.history^value"])

	motadataDBMonitors := utils.ToStringList(motadataDBTable[utils.ObjectId])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+motadataDBMonitors[index]] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)

		} else {

			assertions.Fail(fmt.Sprintf("key mismatched %v key", key))
		}
	}
}

// status flap vertical scalar with one monitor missing

func TestDrillDownStatusFlapLast2DaysMissingMonitorData(t *testing.T) {

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-statusflaphistory-scalar-vertical-missingmonitor.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _1HourGranularity

	clickHouseQuery := "select monitor_id,duration,status, timestamp*1000 as Timestamp from statusTableMetric where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id in [0,2] order by timestamp "

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDurations []int64

	var clickHouseStatues []string

	var clickHouseObjects []int64

	var clickHouseTicks []int64

	clickHouseValue := make(map[string]struct{})

	for rows.Next() {

		var tick int64

		var duration int32

		var status string

		var monitor int32

		err = rows.Scan(&monitor, &duration, &status, &tick)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickHouseObjects = append(clickHouseObjects, int64(monitor))

		clickHouseDurations = append(clickHouseDurations, int64(duration))

		clickHouseStatues = append(clickHouseStatues, status)

	}

	clickHouseValue = prepareClickhouseStatusFlapResult(clickHouseDurations, clickHouseObjects, clickHouseTicks, clickHouseStatues, nil, nil)

	motadataDBValues := make(map[string]struct{})

	motadataDBTicks := utils.ToINT64Values(motadataDBTable["Timestamp"])

	motadataDBDurations := utils.ToINT64Values(motadataDBTable["duration^value"])

	motadataDBStatuses := utils.ToStringList(motadataDBTable["status.flap.history^value"])

	motadataDBMonitors := utils.ToStringList(motadataDBTable[utils.ObjectId])

	for index := range motadataDBTicks {

		motadataDBValues[codec.INT64ToStringValue(motadataDBDurations[index])+utils.GroupSeparator+motadataDBStatuses[index]+utils.GroupSeparator+codec.INT64ToStringValue(motadataDBTicks[index])+
			utils.GroupSeparator+motadataDBMonitors[index]] = struct{}{}
	}

	assertions.Equal(len(clickHouseValue), len(motadataDBValues))

	for key := range clickHouseValue {

		if _, ok := motadataDBValues[key]; ok {

			assertions.True(true)

		} else {

			assertions.Fail(fmt.Sprintf("key mismatched %v key", key))
		}
	}
}

// netpath testcases
func TestDrillDownNetRouteStatusMetricV1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	queryBytes, _ := os.ReadFile(testDir + "drilldown-netrouteuptimepercent.json")

	timeline := utils.GetTimeline(Last6Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp ,metric , percent  from netRouteStatusMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and object_id = 1 and metric = 'netroute.uptime'  order by Timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var percentages []float64

	var metrics []string

	for rows.Next() {

		var tick int64

		var percent float64

		var metric string

		err = rows.Scan(&tick, &metric, &percent)

		metrics = append(metrics, metric)

		clickHouseTicks = append(clickHouseTicks, tick)

		percentages = append(percentages, utils.ToFixed(percent))
	}

	ticks := utils.InterfaceToINT64Values(motadataDBTable["Timestamp"])

	clickhousePercentages, ticks := removeDummyFLOAT64ValueSlice(motadataDBTable["netroute.uptime.percent^value"], motadataDBTable["Timestamp"])

	assertions.Equal(ticks, clickHouseTicks)

	assertValuesHavingErrorTolerance(clickhousePercentages, percentages, assertions)

}

func TestDrillDownNetRouteStatusMetricV2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	queryBytes, _ := os.ReadFile(testDir + "drilldown-netrouteuptimeseconds.json")

	timeline := utils.GetTimeline(Last6Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp ,metric , seconds  from netRouteStatusMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and object_id = 1 and metric = 'netroute.uptime' and seconds > 0  order by Timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickhouseSeconds []int64

	var metrics []string

	for rows.Next() {

		var tick int64

		var second int64

		var metric string

		err = rows.Scan(&tick, &metric, &second)

		metrics = append(metrics, metric)

		clickHouseTicks = append(clickHouseTicks, tick)

		clickhouseSeconds = append(clickhouseSeconds, second)
	}

	ticks := utils.InterfaceToINT64Values(motadataDBTable["Timestamp"])

	seconds, ticks, _, _ := removeDummyINT64ValueSlice(motadataDBTable["netroute.uptime.seconds^value"], motadataDBTable["Timestamp"])

	assertions.Equal(ticks, clickHouseTicks)

	assertions.Equal(seconds, clickhouseSeconds)

}

func TestDrillDownNetRouteToday(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	queryBytes, _ := os.ReadFile(testDir + "drilldown-netroutepinglatency-netroutelostpacket.json")

	timeline := utils.GetTimeline(Today)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp , netroute_ping_latency , netroute_packet_lost from netRouteMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and object_id = 1 order by Timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var netRoutePingLatencies []float64

	var netRouteLostPackets []int64

	for rows.Next() {

		var tick int64

		var pingLatency float64

		var pingLostPackets int64

		err = rows.Scan(&tick, &pingLatency, &pingLostPackets)

		clickHouseTicks = append(clickHouseTicks, tick)

		netRoutePingLatencies = append(netRoutePingLatencies, utils.ToFixed(pingLatency))

		netRouteLostPackets = append(netRouteLostPackets, pingLostPackets)
	}

	ticks := utils.InterfaceToINT64Values(motadataDBTable["Timestamp"])

	pingLatencies, ticks := removeDummyFLOAT64ValueSlice(motadataDBTable[writer.NetRoutePingLatency+utils.KeySeparator+utils.Value], motadataDBTable["Timestamp"])

	pingLostPackets, ticks, _, _ := removeDummyINT64ValueSlice(motadataDBTable[writer.NetRoutePacketLost+utils.KeySeparator+utils.Value], motadataDBTable["Timestamp"])

	assertions.Equal(ticks, clickHouseTicks)

	assertValuesHavingErrorTolerance(netRoutePingLatencies, pingLatencies, assertions)

	assertions.Equal(pingLostPackets, netRouteLostPackets)

	tick := ticks[rand.Intn(len(ticks))]

	key := "1" + utils.KeySeparator + utils.INT64ToStringValue(tick/1000) + utils.KeySeparator + writer.NetRouteEvent

	dataPoint := utils.MotadataMap{
		DataPoint:     "netroute.event",
		Aggregator:    "",
		"entity.type": "Monitor",
		EntityKeys: utils.MotadataMap{
			key: datastore.NetRouteEventPlugin,
		},
		utils.Plugins: []string{datastore.NetRouteEventPlugin},
	}

	delete(queryContext.GetMapValue(VisualizationDataSources), Entities)

	queryContext.GetMapValue(VisualizationDataSources)[DataPoints] = []utils.MotadataMap{dataPoint}

	queryContext.GetMapValue(VisualizationDataSources)[utils.Plugins] = []string{datastore.NetRouteEventPlugin}

	queryContext.GetMapValue(VisualizationDataSources)[EntityKeys] = utils.MotadataMap{
		key: datastore.NetRouteEventPlugin,
	}
	queryContext.GetMapValue(VisualizationDataSources)[Entities] = utils.MotadataMap{
		"1": datastore.NetRouteEventPlugin,
	}

	queryContext.GetMapValue(VisualizationDataSources)["entity.type"] = "NetRoute"

	motadataDBTable, errs = notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery = "select netroute_event  from netRouteMetrics where timestamp = " + utils.INT64ToStringValue(tick/1000) + "  and object_id = 1 "

	rows, err = connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	assertions.True(rows.Next())

	var event string

	rows.Scan(&event)

	assertions.Equal(motadataDBTable[writer.NetRouteEvent+utils.KeySeparator+utils.Value][0].(string), event)

}

func TestDrillDownNetRouteTodayType2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	queryBytes, _ := os.ReadFile(testDir + "drilldown-netroutepinglatency.json")

	timeline := utils.GetTimeline(Today)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery := "select object_id ,  (timestamp)*1000 as Timestamp , netroute_ping_latency , netroute_packet_lost from netRouteMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and object_id in ( 1 , 2 ) order by Timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var netRoutePingLatencies []float64

	var netRouteLostPackets []int64

	var clickHouseMonitors []int64

	for rows.Next() {

		var objectId int64

		var tick int64

		var pingLatency float64

		var pingLostPackets int64

		err = rows.Scan(&objectId, &tick, &pingLatency, &pingLostPackets)

		clickHouseTicks = append(clickHouseTicks, tick)

		netRoutePingLatencies = append(netRoutePingLatencies, utils.ToFixed(pingLatency))

		netRouteLostPackets = append(netRouteLostPackets, pingLostPackets)

		clickHouseMonitors = append(clickHouseMonitors, objectId)
	}

	ticks := utils.InterfaceToINT64Values(motadataDBTable["Timestamp"])

	motadataDbResult := utils.MotadataMap{}

	clickhouseResult := utils.MotadataMap{}

	for index, netRoute := range motadataDBTable["netroute.id"] {

		if !motadataDbResult.Contains(codec.ToString(netRoute)) {

			motadataDbResult[codec.ToString(netRoute)] = make([][]interface{}, 2)
		}

		motadataDbResult[codec.ToString(netRoute)].([][]interface{})[0] = append(motadataDbResult[codec.ToString(netRoute)].([][]interface{})[0], ticks[index])

		motadataDbResult[codec.ToString(netRoute)].([][]interface{})[1] = append(motadataDbResult[codec.ToString(netRoute)].([][]interface{})[1], motadataDBTable[writer.NetRoutePingLatency+utils.KeySeparator+utils.Value][index])
	}

	for index, monitor := range clickHouseMonitors {

		if !clickhouseResult.Contains(codec.INT64ToStringValue(monitor)) {

			clickhouseResult[codec.INT64ToStringValue(monitor)] = make([][]interface{}, 2)
		}

		clickhouseResult[codec.INT64ToStringValue(monitor)].([][]interface{})[0] = append(clickhouseResult[codec.INT64ToStringValue(monitor)].([][]interface{})[0], clickHouseTicks[index])

		clickhouseResult[codec.INT64ToStringValue(monitor)].([][]interface{})[1] = append(clickhouseResult[codec.INT64ToStringValue(monitor)].([][]interface{})[1], netRoutePingLatencies[index])

	}

	assertions.Equal(clickhouseResult["1"].([][]interface{})[0], motadataDbResult["1"].([][]interface{})[0])

	assertions.Equal(clickhouseResult["2"].([][]interface{})[0], motadataDbResult["2"].([][]interface{})[0])

	assertValuesHavingErrorTolerance(utils.InterfaceToFLOAT64Values(clickhouseResult["1"].([][]interface{})[1]), utils.InterfaceToFLOAT64Values(motadataDbResult["1"].([][]interface{})[1]), assertions)

	assertValuesHavingErrorTolerance(utils.InterfaceToFLOAT64Values(clickhouseResult["2"].([][]interface{})[1]), utils.InterfaceToFLOAT64Values(motadataDbResult["2"].([][]interface{})[1]), assertions)

}

func TestDrillDownNetRouteLast24Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	queryBytes, _ := os.ReadFile(testDir + "drilldown-netroutepinglatency-netroutelostpacket.json")

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp , netroute_ping_latency , netroute_packet_lost from netRouteMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and object_id = 1 order by Timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var netRoutePingLatencies []float64

	var netRouteLostPackets []int64

	for rows.Next() {

		var tick int64

		var pingLatency float64

		var pingLostPackets int64

		err = rows.Scan(&tick, &pingLatency, &pingLostPackets)

		clickHouseTicks = append(clickHouseTicks, tick)

		netRoutePingLatencies = append(netRoutePingLatencies, utils.ToFixed(pingLatency))

		netRouteLostPackets = append(netRouteLostPackets, pingLostPackets)
	}

	ticks := utils.InterfaceToINT64Values(motadataDBTable["Timestamp"])

	pingLatencies, ticks := removeDummyFLOAT64ValueSlice(motadataDBTable[writer.NetRoutePingLatency+utils.KeySeparator+utils.Value], motadataDBTable["Timestamp"])

	pingLostPackets, ticks, _, _ := removeDummyINT64ValueSlice(motadataDBTable[writer.NetRoutePacketLost+utils.KeySeparator+utils.Value], motadataDBTable["Timestamp"])

	assertions.Equal(ticks, clickHouseTicks)

	assertValuesHavingErrorTolerance(netRoutePingLatencies, pingLatencies, assertions)

	assertions.Equal(pingLostPackets, netRouteLostPackets)

	tick := ticks[rand.Intn(len(ticks))]

	key := "1" + utils.KeySeparator + utils.INT64ToStringValue(tick/1000) + utils.KeySeparator + writer.NetRouteEvent

	dataPoint := utils.MotadataMap{
		DataPoint:     "netroute.event",
		Aggregator:    "",
		"entity.type": "Monitor",
		EntityKeys: utils.MotadataMap{
			key: datastore.NetRouteEventPlugin,
		},
		utils.Plugins: []string{datastore.NetRouteEventPlugin},
	}

	delete(queryContext.GetMapValue(VisualizationDataSources), Entities)

	queryContext.GetMapValue(VisualizationDataSources)[DataPoints] = []utils.MotadataMap{dataPoint}

	queryContext.GetMapValue(VisualizationDataSources)[utils.Plugins] = []string{datastore.NetRouteEventPlugin}

	queryContext.GetMapValue(VisualizationDataSources)[EntityKeys] = utils.MotadataMap{
		key: datastore.NetRouteEventPlugin,
	}
	queryContext.GetMapValue(VisualizationDataSources)[Entities] = utils.MotadataMap{
		"1": datastore.NetRouteEventPlugin,
	}

	queryContext.GetMapValue(VisualizationDataSources)["entity.type"] = "NetRoute"

	motadataDBTable, errs = notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery = "select netroute_event  from netRouteMetrics where timestamp = " + utils.INT64ToStringValue(tick/1000) + "  and object_id = 1 "

	rows, err = connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	assertions.True(rows.Next())

	var event string

	rows.Scan(&event)

	assertions.Equal(motadataDBTable[writer.NetRouteEvent+utils.KeySeparator+utils.Value][0].(string), event)

}

func TestDrillDownNetRouteLast6Hours(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	queryBytes, _ := os.ReadFile(testDir + "drilldown-netroutepinglatency-netroutelostpacket.json")

	timeline := utils.GetTimeline(Last6Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp , netroute_ping_latency , netroute_packet_lost from netRouteMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and object_id = 1 order by Timestamp desc"

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var netRoutePingLatencies []float64

	var netRouteLostPackets []int64

	for rows.Next() {

		var tick int64

		var pingLatency float64

		var pingLostPackets int64

		err = rows.Scan(&tick, &pingLatency, &pingLostPackets)

		clickHouseTicks = append(clickHouseTicks, tick)

		netRoutePingLatencies = append(netRoutePingLatencies, utils.ToFixed(pingLatency))

		netRouteLostPackets = append(netRouteLostPackets, pingLostPackets)
	}

	ticks := utils.InterfaceToINT64Values(motadataDBTable["Timestamp"])

	pingLatencies, ticks := removeDummyFLOAT64ValueSlice(motadataDBTable[writer.NetRoutePingLatency+utils.KeySeparator+utils.Value], motadataDBTable["Timestamp"])

	pingLostPackets, ticks, _, _ := removeDummyINT64ValueSlice(motadataDBTable[writer.NetRoutePacketLost+utils.KeySeparator+utils.Value], motadataDBTable["Timestamp"])

	assertions.Equal(ticks, clickHouseTicks)

	assertValuesHavingErrorTolerance(netRoutePingLatencies, pingLatencies, assertions)

	assertions.Equal(pingLostPackets, netRouteLostPackets)

	tick := ticks[rand.Intn(len(ticks))]

	key := "1" + utils.KeySeparator + utils.INT64ToStringValue(tick/1000) + utils.KeySeparator + writer.NetRouteEvent

	dataPoint := utils.MotadataMap{
		DataPoint:     "netroute.event",
		Aggregator:    "",
		"entity.type": "Monitor",
		EntityKeys: utils.MotadataMap{
			key: datastore.NetRouteEventPlugin,
		},
		utils.Plugins: []string{datastore.NetRouteEventPlugin},
	}

	delete(queryContext.GetMapValue(VisualizationDataSources), Entities)

	queryContext.GetMapValue(VisualizationDataSources)[DataPoints] = []utils.MotadataMap{dataPoint}

	queryContext.GetMapValue(VisualizationDataSources)[utils.Plugins] = []string{datastore.NetRouteEventPlugin}

	queryContext.GetMapValue(VisualizationDataSources)[EntityKeys] = utils.MotadataMap{
		key: datastore.NetRouteEventPlugin,
	}
	queryContext.GetMapValue(VisualizationDataSources)[Entities] = utils.MotadataMap{
		"1": datastore.NetRouteEventPlugin,
	}

	queryContext.GetMapValue(VisualizationDataSources)["entity.type"] = "NetRoute"

	motadataDBTable, errs = notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	clickHouseQuery = "select netroute_event  from netRouteMetrics where timestamp = " + utils.INT64ToStringValue(tick/1000) + "  and object_id = 1 "

	rows, err = connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	assertions.True(rows.Next())

	var event string

	rows.Scan(&event)

	assertions.Equal(motadataDBTable[writer.NetRouteEvent+utils.KeySeparator+utils.Value][0].(string), event)

}

// float with missing column
func TestDrillDownScalarMetricSystemCPUPercentLastQuarterMissingColumn(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(LastQuarter)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, system_cpu_percent from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-systemcpupercent.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []float64

	for rows.Next() {

		var tick int64

		var cpuValue float64

		err = rows.Scan(&tick, &cpuValue)

		clickHouseValues = append(clickHouseValues, codec.ToFixed(cpuValue))

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertValuesHavingErrorTolerance(clickHouseValues, utils.InterfaceToFLOAT64Values(motadataDBTable["system.cpu.percent^value"]), assertions)

}

// int8
func TestDrillDownScalarMetricVLANPortsLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, vlan_ports from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-vlanports.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	for rows.Next() {

		var tick int64

		var vlanPorts int64

		err = rows.Scan(&tick, &vlanPorts)

		clickHouseValues = append(clickHouseValues, vlanPorts)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseValues, writer.INT64InterfaceToINT64Values(motadataDBTable["vlan.ports^value"]))

}

// int8 with missing column
func TestDrillDownScalarMetricVLANPortsLast2DaysMissingColumn(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, vlan_ports from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-vlanports-missingcolumn.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Contains(motadataDBTable, "vlan.ports1^value")

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	for rows.Next() {

		var tick int64

		var vlanPorts int64

		err = rows.Scan(&tick, &vlanPorts)

		clickHouseValues = append(clickHouseValues, vlanPorts)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseValues, writer.INT64InterfaceToINT64Values(motadataDBTable["vlan.ports^value"]))

}

// int16
func TestDrillDownScalarMetricInterfacesLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, interfaces from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-interfaces.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	for rows.Next() {

		var tick int64

		var interfaces int64

		err = rows.Scan(&tick, &interfaces)

		clickHouseValues = append(clickHouseValues, interfaces)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseValues, writer.INT64InterfaceToINT64Values(motadataDBTable["interfaces^value"]))

}

// int16 with missing column
func TestDrillDownScalarMetricInterfacesLast2DaysMissingColumn(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, interfaces from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-interfaces-missingcolumn.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Contains(motadataDBTable, "interfaces1^value")

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	for rows.Next() {

		var tick int64

		var interfaces int64

		err = rows.Scan(&tick, &interfaces)

		clickHouseValues = append(clickHouseValues, interfaces)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseValues, writer.INT64InterfaceToINT64Values(motadataDBTable["interfaces^value"]))

}

// int32
func TestDrillDownScalarMetricSystemMemoryCommittedBytesLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, system_memory_committed_bytes from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-systemmemorycommittedbytes.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	for rows.Next() {

		var tick int64

		var systemMemoryCommittedBytes int64

		err = rows.Scan(&tick, &systemMemoryCommittedBytes)

		clickHouseValues = append(clickHouseValues, systemMemoryCommittedBytes)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseValues, writer.INT64InterfaceToINT64Values(motadataDBTable["system.memory.committed.bytes^value"]))

}

// int64
func TestDrillDownScalarMetricSystemNetworkINBytesLast2Days(t *testing.T) {

	defer cache.Clear()

	connection, ctx, err := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	clickHouseQuery := "select (timestamp)*1000 as Timestamp, system_network_in_bytes from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and   timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and monitor_id = 1 order by Timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-notspecified-scalar-metric-systemnetworkinbytes.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _5MinGranularity

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTicks []int64

	var clickHouseValues []int64

	for rows.Next() {

		var tick int64

		var systemNetworkINBytes int64

		err = rows.Scan(&tick, &systemNetworkINBytes)

		clickHouseValues = append(clickHouseValues, systemNetworkINBytes)

		clickHouseTicks = append(clickHouseTicks, tick)

	}

	assertions.EqualValues(clickHouseTicks, writer.INT64InterfaceToINT64Values(motadataDBTable["Timestamp"]))

	assertions.EqualValues(clickHouseValues, writer.INT64InterfaceToINT64Values(motadataDBTable["system.network.in.bytes^value"]))

}

//trap count chart

func TestDrillDownTrapMessageLast24HoursGroupByTrapSeverityv1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-count-trapmessage.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _1HourGranularity

	granularity := codec.INTToStringValue(getGranularity(queryContext))

	clickHouseQuery := "select (trap_severity), toUnixTimestamp(toStartOfInterval(toDateTime(timestamp,'UTC'),INTERVAL " + granularity + " Second))*1000 as Timestamp, count(trap_message) as \"" + CountTrapMessage + "\" from trapTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by trap_severity,Timestamp order by Timestamp"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDBTicks []int64

	var clickHouseSeries = make(map[string][]int64)

	for rows.Next() {

		var tick uint64

		var count uint64

		var trapSeverity string

		err = rows.Scan(&trapSeverity, &tick, &count)

		clickHouseSeries[trapSeverity+utils.KeySeparator+CountTrapMessage] = append(clickHouseSeries[trapSeverity+utils.KeySeparator+CountTrapMessage], int64(count))

		if !utils.ContainsINT64Value(clickHouseDBTicks, int64(tick)) {

			clickHouseDBTicks = append(clickHouseDBTicks, int64(tick))
		}

	}

	motadataDBTicks, groups := removeDummyINT64ValueTablev2(motadataDBTable)

	assertions.EqualValues(clickHouseDBTicks[1:], motadataDBTicks[1:])

	for key, series := range groups {

		assertions.Equal(series, clickHouseSeries[key])

	}
}

// multiple records in a single tick
func TestDrillDownTrapMessageLast24HoursGroupByTrapSeverityv2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last24Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "histogram-count-trapmessage-type1.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	queryContext[VisualizationGranularity] = _1HourGranularity

	granularity := codec.INTToStringValue(getGranularity(queryContext))

	clickHouseQuery := "select (trap_severity), toUnixTimestamp(toStartOfInterval(toDateTime(timestamp,'UTC'),INTERVAL " + granularity + " Second))*1000 as Timestamp, count(trap_message) as \"" + CountTrapMessage + "\" from trapTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by trap_severity,Timestamp order by Timestamp"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseDBTicks []int64

	var clickHouseSeries = make(map[string][]int64)

	for rows.Next() {

		var tick uint64

		var count uint64

		var trapSeverity string

		err = rows.Scan(&trapSeverity, &tick, &count)

		clickHouseSeries[trapSeverity+utils.KeySeparator+CountTrapMessage] = append(clickHouseSeries[trapSeverity+utils.KeySeparator+CountTrapMessage], int64(count))

		if !utils.ContainsINT64Value(clickHouseDBTicks, int64(tick)) {

			clickHouseDBTicks = append(clickHouseDBTicks, int64(tick))
		}

	}

	motadataDBTicks, groups := removeDummyINT64ValueTablev2(motadataDBTable)

	assertions.EqualValues(clickHouseDBTicks, motadataDBTicks)

	for key, series := range groups {

		assertions.Equal(series, clickHouseSeries[key])

	}
}

// grid last values
func TestDrillDownTrapMessageTrapNameTrapOIDEventSourceLast1HourGroupByEventSourceTrapOIDv1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-last-trapmessage.json")

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select argMax(trap_name,timestamp),argMax(trap_oid,timestamp) ,argMax(event_source,timestamp) , argMax(trap_message,timestamp) ,argMax(trap_vendor,timestamp) ,argMax(enterprise_id,timestamp),argMax(trap_version,timestamp),argMax(timestamp,timestamp)*1000  from trapTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by event_source,trap_oid"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTable := make(map[string][]interface{})

	for rows.Next() {

		var trapName string

		var trapOID string

		var eventSource string

		var trapMessage string

		var trapVendor string

		var trapEnterpriseId string

		var trapVersion int64

		var timestamp int64

		err = rows.Scan(&trapName, &trapOID, &eventSource, &trapMessage, &trapVendor, &trapEnterpriseId, &trapVersion, &timestamp)

		clickHouseTable[eventSource+utils.GroupSeparator+trapOID] = []interface{}{trapMessage, trapVendor, trapEnterpriseId, codec.INT64ToStringValue(trapVersion), timestamp}

	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	eventSources := utils.ToStringList(motadataDBTable[utils.EventSource])

	trapOIDs := utils.ToStringList(motadataDBTable[utils.TrapOID])

	motadataDBValues := make(map[string][]interface{})

	for index := range eventSources {

		motadataDBValues[eventSources[index]+utils.GroupSeparator+trapOIDs[index]] = []interface{}{utils.ToStringList(motadataDBTable[LastTrapMessage])[index],
			utils.ToStringList(motadataDBTable[LastTrapVendor])[index], utils.ToStringList(motadataDBTable[LastTrapEnterpriseId])[index], utils.ToStringList(motadataDBTable[LastTrapVersion])[index],
			utils.ToINT64Values(motadataDBTable["timestamp"+utils.KeySeparator+"last"])[index],
		}

	}

	for key := range motadataDBValues {

		assertions.Equal(clickHouseTable[key], motadataDBValues[key], key)
	}
}

// grid last values multiple records
func TestDrillDownTrapMessageTrapNameTrapOIDEventSourceLast1HourGroupByEventSourceTrapOIDv2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-last-trapmessage-type1.json")

	timeline := utils.GetTimeline(Today)

	clickHouseQuery := "select trap_name,trap_oid ,event_source,trap_message,enterprise_id,trap_version,timestamp*1000 from trapTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix())

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTable := make(map[string]struct{})

	for rows.Next() {

		var trapName string

		var trapOID string

		var eventSource string

		var trapMessage string

		var trapEnterpriseId string

		var trapVersion string

		var timestamp int64

		err = rows.Scan(&trapName, &trapOID, &eventSource, &trapMessage, &trapEnterpriseId, &trapVersion, &timestamp)

		clickHouseTable[eventSource+utils.GroupSeparator+trapOID+utils.GroupSeparator+trapMessage+utils.GroupSeparator+trapEnterpriseId+utils.GroupSeparator+trapVersion+utils.GroupSeparator+codec.ToString(timestamp)] = struct{}{}

	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	eventSources := utils.ToStringList(motadataDBTable[utils.EventSource])

	trapOIDs := utils.ToStringList(motadataDBTable[utils.TrapOID])

	trapMessages := utils.ToStringList(motadataDBTable[LastTrapMessage])

	trapEnterpriseIds := utils.ToStringList(motadataDBTable[LastTrapEnterpriseId])

	trapVersions := utils.ToStringList(motadataDBTable[LastTrapVersion])

	timestamps := utils.ToINT64Values(motadataDBTable["timestamp"+utils.KeySeparator+"last"])

	motadataDBValues := make(map[string]struct{})

	for index := range eventSources {

		motadataDBValues[eventSources[index]+utils.GroupSeparator+trapOIDs[index]+utils.GroupSeparator+trapMessages[index]+utils.GroupSeparator+trapEnterpriseIds[index]+
			utils.GroupSeparator+trapVersions[index]+utils.GroupSeparator+codec.ToString(timestamps[index])] = struct{}{}

	}

	for key := range motadataDBValues {

		if _, ok := clickHouseTable[key]; ok {

			assertions.True(true, " one of the value matched of the same tick")

		} else {

			assertions.False(true, " none of the value matched of the same tick")

		}

	}
}

/*
	scenario :-

	get the absolute last value of the blob column to test the equation in vertical.go

*/

func TestDrillDownTrapMessageTrapNameTrapOIDEventSourceLast1HourGroupByEventSource(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-last-trapmessage.json")

	currentTime := time.Now().UTC()

	timeline := utils.GetTimeline(Today)

	/*
		because in all the tables that are inserted we insert it for current day till end. that is why the timeline is current day's end tick or tomorrow's start tick as ToDateTime
	*/

	timeline[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, time.UTC).UnixMilli()

	timeline[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 0, 0, 0, 0, time.UTC).UnixMilli()

	clickHouseQuery := "select argMax(trap_name,timestamp),argMax(trap_oid,timestamp) ,argMax(event_source,timestamp) , argMax(trap_message,timestamp) ,argMax(trap_vendor,timestamp) ,argMax(enterprise_id,timestamp),argMax(trap_version,timestamp),argMax(timestamp,timestamp)*1000  from trapTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by event_source,trap_oid"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTable := make(map[string][]interface{})

	for rows.Next() {

		var trapName string

		var trapOID string

		var eventSource string

		var trapMessage string

		var trapVendor string

		var trapEnterpriseId string

		var trapVersion int64

		var timestamp int64

		err = rows.Scan(&trapName, &trapOID, &eventSource, &trapMessage, &trapVendor, &trapEnterpriseId, &trapVersion, &timestamp)

		clickHouseTable[eventSource+utils.GroupSeparator+trapOID] = []interface{}{trapMessage, trapVendor, trapEnterpriseId, codec.INT64ToStringValue(trapVersion), timestamp}

	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	eventSources := utils.ToStringList(motadataDBTable[utils.EventSource])

	trapOIDs := utils.ToStringList(motadataDBTable[utils.TrapOID])

	motadataDBValues := make(map[string][]interface{})

	for index := range eventSources {

		motadataDBValues[eventSources[index]+utils.GroupSeparator+trapOIDs[index]] = []interface{}{utils.ToStringList(motadataDBTable[LastTrapMessage])[index],
			utils.ToStringList(motadataDBTable[LastTrapVendor])[index], utils.ToStringList(motadataDBTable[LastTrapEnterpriseId])[index], utils.ToStringList(motadataDBTable[LastTrapVersion])[index],
			utils.ToINT64Values(motadataDBTable["timestamp"+utils.KeySeparator+"last"])[index],
		}

	}

	for key := range motadataDBValues {

		assertions.Equal(clickHouseTable[key], motadataDBValues[key], key)
	}
}

//grid count column with filter

func TestDrillDownTrapMessageLast1MonthGroupByEventSourceTrapOIDFilterByTrapOIDv1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-count-trapmessage-filterby-trapoid.json")

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select event_source,trap_oid ,count(trap_message) as \"" + CountTrapMessage + "\" from trapTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and trap_oid ='1.0.0.1' group by event_source,trap_oid order by \"" + CountTrapMessage + "\" desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHousetable := make(map[string]int64)

	for rows.Next() {

		var eventSource string

		var trapOID string

		var value uint64

		err = rows.Scan(&eventSource, &trapOID, &value)

		clickHousetable[eventSource+utils.GroupSeparator+trapOID] = int64(value)

	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	eventSources := utils.ToStringList(motadataDBTable[utils.EventSource])

	trapOIDs := utils.ToStringList(motadataDBTable[utils.TrapOID])

	motadataDBValues := utils.ToINT64Values(motadataDBTable[CountTrapMessage])

	for index := range eventSources {

		assertions.Equal(clickHousetable[eventSources[index]+utils.GroupSeparator+trapOIDs[index]], motadataDBValues[index])

	}
}

// vertical qualification
func TestDrillDownPolicyMessageSeverityMetricFilterByPolicyId(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-policymessage-severity-metric-filterby-policyid.json")

	timeline := utils.GetTimeline(Last1Hour)

	//here we are using timestamp in comparing time because in case of vertical we don't have timestamp[Rounded times]

	clickHouseQuery := "select message, severity,metric,(timestamp *1000) as Timestamp from policyTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and policyId in [97252354583036] order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var expectedMessages []string

	var expectedSeverity []string

	var expectedMetrics []string

	var expectedTimestamps []int64

	for rows.Next() {

		var message string

		var severity string

		var metric string

		var timestamp int64

		err = rows.Scan(&message, &severity, &metric, &timestamp)

		expectedMessages = append(expectedMessages, message)

		expectedSeverity = append(expectedSeverity, severity)

		expectedMetrics = append(expectedMetrics, metric)

		expectedTimestamps = append(expectedTimestamps, timestamp)

	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.EqualValues(expectedMessages, utils.ToStringList(motadataDBTable["message^value"]))

	assertions.EqualValues(expectedSeverity, utils.ToStringList(motadataDBTable["severity^value"]))

	assertions.EqualValues(expectedMetrics, utils.ToStringList(motadataDBTable["metric^value"]))

	assertions.EqualValues(expectedTimestamps, utils.ToINT64Values(motadataDBTable["Timestamp"]))

}

//grid * aggregation avg

func TestDrillDownLast1MonthGroupByMonitor(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-avg-scalar-metric-allmetrics-groupby-monitor.json")

	timeline := utils.GetTimeline(Last1Month)

	clickHouseQuery := "select monitor_id as \"monitor\", avg(system_cpu_percent) as \"" + AvgSystemCPUPercent + "\"from scalar where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by monitor_id order by \"" + AvgSystemCPUPercent + "\" desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTable := make(map[string]float64)

	for rows.Next() {

		var monitorId int32

		var value float64

		err = rows.Scan(&monitorId, &value)

		clickHouseTable[codec.INT32ToStringValue(monitorId)] = codec.ToFixed(value)

	}

	assertions.NotNil(motadataDBTable)

	monitors := writer.INT32InterfaceToINT32Values(motadataDBTable[datastore.Monitor])

	motadataDBCPUValues := utils.InterfaceToFLOAT64Values(motadataDBTable[AvgSystemCPUPercent])

	for index, value := range monitors {

		assertValueHavingErrorTolerance(clickHouseTable[codec.INT32ToStringValue(value)], motadataDBCPUValues[index], assertions)

	}

}

//grid * aggregation last

func TestDrillDownLast1MonthGroupByMonitorType1(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-last-scalar-metric-allmetrics-groupby-monitor.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

}

func TestDrillDownLast1MonthGroupByGroup(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-last-scalar-metric-allmetrics-groupby-group.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

}

func TestDrillDownLast1MonthGroupByTag(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-last-instance-metric-allmetrics-groupby-tag.json")

	timeline := utils.GetTimeline(Last1Month)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

}

//trap testcase

func TestDrillDownTrapMessageLast1MonthGroupByEventSourceTrapOIDFilterByTrapOIDv2(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-parting.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select timestamp,trap_message,enterprise_id  from trapTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source='10.20.40.140' and trap_oid ='1.0.0.1.0' order by timestamp desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTableTimestamp []int64

	var clickHouseTableMessages []string

	var clickHouseTableTrapEnterpriseId []string

	for rows.Next() {

		var trapMessage string

		var timestamp int64

		var enterpriseId string

		err = rows.Scan(&timestamp, &trapMessage, &enterpriseId)

		clickHouseTableTimestamp = append(clickHouseTableTimestamp, timestamp*1000)

		clickHouseTableMessages = append(clickHouseTableMessages, trapMessage)

		clickHouseTableTrapEnterpriseId = append(clickHouseTableTrapEnterpriseId, enterpriseId)
	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	motadataDBTableMessages := utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.KeySeparator+"value"])

	motadataDBTableTrapEnterpriseId := utils.ToStringList(motadataDBTable[utils.TrapEnterpriseId+utils.KeySeparator+"value"])

	utils.SortStringValues(clickHouseTableMessages)

	utils.SortStringValues(motadataDBTableMessages)

	utils.SortStringValues(clickHouseTableTrapEnterpriseId)

	utils.SortStringValues(motadataDBTableTrapEnterpriseId)

	assertions.Equal(clickHouseTableMessages, motadataDBTableMessages)

	assertions.Equal(clickHouseTableTrapEnterpriseId, motadataDBTableTrapEnterpriseId)
}

func TestDrillDownTrapMessageLast6HoursGroupByEventSourceTrapOIDFilterByTrapOIDv3(t *testing.T) {

	defer cache.Clear()

	maxRecords := utils.MaxHistoricalRecords

	defer func() {

		utils.MaxHistoricalRecords = maxRecords
	}()

	utils.MaxHistoricalRecords = 30

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-parting.json")

	timeline := utils.GetTimeline(Last6Hours)

	clickHouseQuery := "select timestamp,trap_message,enterprise_id  from trapTable1 where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " and event_source='10.20.40.140' and trap_oid ='1.0.0.1.0' order by timestamp desc limit 30"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	var clickHouseTableTimestamp []int64

	var clickHouseTableMessages []string

	var clickHouseTableTrapEnterpriseId []string

	for rows.Next() {

		var trapMessage string

		var timestamp int64

		var enterpriseId string

		err = rows.Scan(&timestamp, &trapMessage, &enterpriseId)

		clickHouseTableTimestamp = append(clickHouseTableTimestamp, timestamp*1000)

		clickHouseTableMessages = append(clickHouseTableMessages, trapMessage)

		clickHouseTableTrapEnterpriseId = append(clickHouseTableTrapEnterpriseId, enterpriseId)
	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	motadataDBTableMessages := utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.KeySeparator+"value"])

	motadataDBTableTrapEnterpriseId := utils.ToStringList(motadataDBTable[utils.TrapEnterpriseId+utils.KeySeparator+"value"])

	utils.SortStringValues(clickHouseTableMessages)

	utils.SortStringValues(motadataDBTableMessages)

	utils.SortStringValues(clickHouseTableTrapEnterpriseId)

	utils.SortStringValues(motadataDBTableTrapEnterpriseId)

	assertions.Equal(clickHouseTableTimestamp, utils.ToINT64Values(motadataDBTable["Timestamp"]))
}

func TestDrillDownTrapMessageScenario1TodayTimeline(t *testing.T) {

	defer cache.Clear()

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-scenario1.json")

	timeline := utils.GetTimeline(Today)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal([]string{"cisco1", "cisco1"}, utils.ToStringList(motadataDBTable[utils.TrapEnterprise+utils.ValueSuffix]))

	assertions.Equal([]string{"high", "high"}, utils.ToStringList(motadataDBTable[utils.TrapSeverity+utils.ValueSuffix]))

	assertions.Equal([]string{"message2", "message1"}, utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.ValueSuffix]))

	assertions.Equal([]string{utils.Empty, utils.Empty}, utils.ToStringList(motadataDBTable[utils.TrapRawMessage+utils.ValueSuffix]))
}

func TestDrillDownTrapMessageScenario2TodayTimeline(t *testing.T) {

	defer cache.Clear()

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-scenario2.json")

	timeline := utils.GetTimeline(Today)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal([]string{"cisco1", "cisco1", "cisco1", "cisco1"}, utils.ToStringList(motadataDBTable[utils.TrapEnterprise+utils.ValueSuffix]))

	assertions.Equal([]string{"high", "high", "high", "high"}, utils.ToStringList(motadataDBTable[utils.TrapSeverity+utils.ValueSuffix]))

	assertions.Equal([]string{"message4", "message3", "message2", "message1"}, utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.ValueSuffix]))

	assertions.Equal([]string{utils.Empty, utils.Empty, "raw.message2", "raw.message1"}, utils.ToStringList(motadataDBTable[utils.TrapRawMessage+utils.ValueSuffix]))
}

func TestDrillDownTrapMessageScenario3Last48HoursTimeline(t *testing.T) {

	defer cache.Clear()

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-scenario3.json")

	timeline := utils.GetTimeline(Last48Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal([]string{"cisco1", "cisco1", "cisco1", "cisco1"}, utils.ToStringList(motadataDBTable[utils.TrapEnterprise+utils.ValueSuffix]))

	assertions.Equal([]string{"high", "high", "high", "high"}, utils.ToStringList(motadataDBTable[utils.TrapSeverity+utils.ValueSuffix]))

	assertions.Equal([]string{"message2", "message1", "message4", "message3"}, utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.ValueSuffix]))

	assertions.Equal([]string{"raw.message2", "raw.message1", utils.Empty, utils.Empty}, utils.ToStringList(motadataDBTable[utils.TrapRawMessage+utils.ValueSuffix]))
}

func TestDrillDownTrapMessageScenario3TodayTimeline(t *testing.T) {

	defer cache.Clear()

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-scenario3.json")

	timeline := utils.GetTimeline(Today)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal([]string{"cisco1", "cisco1"}, utils.ToStringList(motadataDBTable[utils.TrapEnterprise+utils.ValueSuffix]))

	assertions.Equal([]string{"high", "high"}, utils.ToStringList(motadataDBTable[utils.TrapSeverity+utils.ValueSuffix]))

	assertions.Equal([]string{"message2", "message1"}, utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.ValueSuffix]))

	assertions.Equal([]string{"raw.message2", "raw.message1"}, utils.ToStringList(motadataDBTable[utils.TrapRawMessage+utils.ValueSuffix]))
}

func TestDrillDownTrapMessageScenario4TodayTimeline(t *testing.T) {

	defer cache.Clear()

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-scenario4.json")

	timeline := utils.GetTimeline(Today)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal([]string{"cisco1", "cisco1"}, utils.ToStringList(motadataDBTable[utils.TrapEnterprise+utils.ValueSuffix]))

	assertions.Equal([]string{"high", "high"}, utils.ToStringList(motadataDBTable[utils.TrapSeverity+utils.ValueSuffix]))

	assertions.Equal([]string{"message2", "message1"}, utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.ValueSuffix]))

	assertions.Equal([]string{utils.Empty, utils.Empty}, utils.ToStringList(motadataDBTable[utils.TrapRawMessage+utils.ValueSuffix]))
}

func TestDrillDownTrapMessageScenario4Last48HoursTimeline(t *testing.T) {

	defer cache.Clear()

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-scenario4.json")

	timeline := utils.GetTimeline(Last48Hours)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal([]string{"cisco1", "cisco1", "cisco1", "cisco1"}, utils.ToStringList(motadataDBTable[utils.TrapEnterprise+utils.ValueSuffix]))

	assertions.Equal([]string{"high", "high", "high", "high"}, utils.ToStringList(motadataDBTable[utils.TrapSeverity+utils.ValueSuffix]))

	assertions.Equal([]string{"message2", "message1", "message4", "message3"}, utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.ValueSuffix]))

	assertions.Equal([]string{utils.Empty, utils.Empty, "raw.message4", "raw.message3"}, utils.ToStringList(motadataDBTable[utils.TrapRawMessage+utils.ValueSuffix]))
}

func TestDrillDownTrapMessageScenario5TodayTimeline(t *testing.T) {

	defer cache.Clear()

	queryBytes, _ := os.ReadFile(testDir + "histogram-allrecords-trapmessage-scenario5.json")

	timeline := utils.GetTimeline(Today)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(queryBytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	assertions.Equal([]string{"cisco1", "cisco1", "cisco1", "cisco1"}, utils.ToStringList(motadataDBTable[utils.TrapEnterprise+utils.ValueSuffix]))

	assertions.Equal([]string{"high", "high", "high", "high"}, utils.ToStringList(motadataDBTable[utils.TrapSeverity+utils.ValueSuffix]))

	assertions.Equal([]string{"message4", "message3", "message2", "message1"}, utils.ToStringList(motadataDBTable[utils.TrapMessage+utils.ValueSuffix]))

	assertions.Equal([]string{"raw.message4", "raw.message3", utils.Empty, utils.Empty}, utils.ToStringList(motadataDBTable[utils.TrapRawMessage+utils.ValueSuffix]))
}

//policy count column

func TestDrillDownSeverityLast1HourGroupByObjectIdSeverityInstancePolicyId(t *testing.T) {

	defer cache.Clear()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	bytes, _ := os.ReadFile(testDir + "grid-count-policyseverity.json")

	timeline := utils.GetTimeline(Last1Hour)

	clickHouseQuery := "select monitor_id,policyId,severity,instance,count(severity) as \"" + CountSeverity + "\" from policyTable where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " group by monitor_id,policyId,severity,instance order by \"" + CountSeverity + "\" desc"

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	startTime := time.Now().UnixMilli()

	datastore.DisableHorizontalAggregations("499998-policy.flap")

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	endTime := time.Now().UnixMilli()

	motadataDBQueryTime := endTime - startTime

	assertions.NotNil(motadataDBTable)

	startTime = time.Now().UnixMilli()

	rows, err := connection.Query(ctx, clickHouseQuery)

	if err != nil {

		panic(err)
	}

	clickHousetable := make(map[string]int64)

	for rows.Next() {

		var objectId int32

		var instance string

		var severity string

		var policyId int64

		var countValue uint64

		err = rows.Scan(&objectId, &policyId, &severity, &instance, &countValue)

		clickHousetable[codec.INT32ToStringValue(objectId)+utils.GroupSeparator+instance+
			utils.GroupSeparator+severity+utils.GroupSeparator+codec.INT64ToStringValue(policyId)] = int64(countValue)

	}

	endTime = time.Now().UnixMilli()

	clickHouseQueryTime := endTime - startTime

	publishTimeTicks(getCurrentFuncName(), clickHouseQueryTime, motadataDBQueryTime)

	assertions.NotNil(motadataDBTable)

	objects := motadataDBTable[utils.ObjectId]

	instances := utils.ToStringList(motadataDBTable[datastore.Instance])

	severities := utils.ToStringList(motadataDBTable[utils.PolicySeverity])

	policies := motadataDBTable[utils.PolicyId]

	motadataDBValues := utils.ToINT64Values(motadataDBTable[CountSeverity])

	for index := range objects {

		key := codec.ToString(objects[index]) + utils.GroupSeparator + instances[index] +
			utils.GroupSeparator + severities[index] + utils.GroupSeparator + codec.ToString(policies[index])

		assertions.Equal(clickHousetable[key], motadataDBValues[index])

	}

}

func TestDrillDownQualifyTicks(t *testing.T) {

	var drillDownExecutor *Executor

	for i := range executors {

		if executors[i].queryEngineType == DrillDown {

			drillDownExecutor = executors[i]

			break
		}

	}

	utils.QueryPlanLogging = true

	assertions := assert.New(t)

	assertions.NotNil(drillDownExecutor)

	drillDownExecutor.qualifiedTickPoolIndex, drillDownExecutor.qualifiedTicks = drillDownExecutor.memoryPool.AcquireINT32Pool(1)

	defer drillDownExecutor.memoryPool.ReleaseINT32Pool(drillDownExecutor.qualifiedTickPoolIndex)

	tick := time.Now().Unix()

	fromTime := utils.UnixToSeconds(tick) - 300

	toTime := utils.UnixToSeconds(tick)

	date := utils.SecondsToDate(fromTime)

	err := drillDownExecutor.qualifyDrillDownTicks(date, fromTime, toTime)

	assertions.NotNil(err) //covers the no qualified ticks

	assertions.True(drillDownExecutor.qualifiedTickElementSize == 0) //covers the no qualified ticks

	drillDownExecutor.dates[date] = map[int32]string{

		fromTime:     "",
		fromTime + 1: "",
		fromTime + 2: "",
	}

	err = drillDownExecutor.qualifyDrillDownTicks(date, fromTime, toTime) //covers the overflow of tick pool

	assertions.Nil(err)

	assertions.True(drillDownExecutor.qualifiedTickElementSize == 3) //fromtime, fromtime+1, fromtime+2

}

func TestDrillDownLoadQueryContext(t *testing.T) {

	t.Skip("debug")

	var drillDownExecutor *Executor

	for i := range executors {

		if executors[i].queryEngineType == DrillDown {

			drillDownExecutor = executors[i]

			break
		}

	}

	utils.QueryPlanLogging = true

	buffer := bytes.Buffer{}

	buffer.WriteByte(1)

	assertions := assert.New(t)

	assertions.NotNil(drillDownExecutor)

	drillDownExecutor.qualifiedTickElementSize = drillDownExecutor.memoryPool.GetPoolLength() + 128*(drillDownExecutor.memoryPool.GetPoolLength()/10000) + 100

	drillDownExecutor.qualifiedTickPoolIndex, drillDownExecutor.qualifiedTicks = drillDownExecutor.memoryPool.AcquireINT32Pool(drillDownExecutor.qualifiedTickElementSize)

	defer drillDownExecutor.memoryPool.ReleaseINT32Pool(drillDownExecutor.qualifiedTickPoolIndex)

	assertions.Equal(chooseTimeInterval([]int{1}, 30), 1)

	tick := time.Now().Unix()

	fromTime := utils.UnixToSeconds(tick)

	for i := 0; i < drillDownExecutor.qualifiedTickElementSize; i++ {

		drillDownExecutor.qualifiedTicks[i] = fromTime

		fromTime++
	}

	drillDownExecutor.packTicks(&buffer)

	context := utils.MotadataMap{

		DrillDownQueryContext: buffer.Bytes(),

		VisualizationDataSources: map[string]interface{}{

			Filters: utils.MotadataMap{

				DataFilter: map[string]interface{}{

					Operator: and,

					Filter: include,

					Groups: []interface{}{

						map[string]interface{}{

							Filter: include,

							Operator: and,

							Conditions: []interface{}{

								map[string]interface{}{},
								map[string]interface{}{},
								map[string]interface{}{},
								map[string]interface{}{},
								map[string]interface{}{},
							},
						},
					},
				},
			},
		},
	}

	path := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator

	storeName := utils.Plugin + utils.HyphenSeparator + datastore.Mappings

	_ = os.RemoveAll(path + utils.Temp + storeName)

	err := os.Rename(path+storeName, path+utils.Temp+storeName)

	assertions.Nil(err)

	defer func() {

		_ = os.RemoveAll(path + storeName)

		err = os.Rename(path+utils.Temp+storeName, path+storeName)

		assertions.Nil(err)

		datastore.Init()
	}()

	datastore.Init()

	utils.Requests = make(chan []byte, utils.DrillDownQueryExecutors)

	go func() {

		for i := 0; i < 2; i++ {

			<-utils.Requests
		}

	}()

	time.Sleep(time.Millisecond * 1)

	drillDownExecutor.request = context

	drillDownExecutor.plugin = datastore.EventSearchPlugin

	drillDownExecutor.qualifiedTickElementSize = 0

	err = drillDownExecutor.loadDrillDownQueryContext()

	assertions.NotNil(err)

	encoder := codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools))

	pluginMappings := datastore.GetStore(storeName, utils.Mapping, true, true, encoder, &utils.Tokenizer{Tokens: make([]string, 30)})

	err = pluginMappings.PutStringMapping("500009-windows.something", encoder)

	assertions.Nil(err)

	drillDownExecutor.qualifiedTickElementSize = 0

	err = drillDownExecutor.loadDrillDownQueryContext()

	assertions.NotNil(err)

}

func TestCheckWorkerColumnGroupOverflow(t *testing.T) {

	worker := workers[0]

	executor := executors[0]

	executor.ShutdownNotifications <- true

	time.Sleep(time.Millisecond * 1)

	go func() {

		<-executor.OverflowNotifications

		executor.OverflowedAcks[0] <- 0

	}()

	worker.columnGroups[0] = intmap.New[uint64, int](worker.poolLength * 5)

	assertions := assert.New(t)

	worker.columnGroups[0].Put(utils.GetHash64([]byte("key")), 0)

	assertions.True(worker.columnGroups[0].Len() == 1)

	floatColumnIndex := worker.setColumnContext(0, "float.column", codec.Float64, false)

	stringColumnIndex := worker.setColumnContext(0, "string.column", codec.String, false)

	assertions.Contains(worker.columns[0], "float.column")

	assertions.Contains(worker.columns[0], "string.column")

	worker.checkOverflow(0, worker.decoders[0].MemoryPool.GetPoolLength()+1)

	assertions.True(worker.columnGroups[0].Len() == 0)

	assertions.NotContains(worker.columns[0], "float.column")

	assertions.NotContains(worker.columns[0], "string.column")

	assertions.Equal(utils.NotAvailable, worker.columnPoolIndices[0][floatColumnIndex])

	assertions.Equal(codec.Int8, worker.columnDataTypes[0][floatColumnIndex])

	assertions.Equal(utils.NotAvailable, worker.columnPoolIndices[0][stringColumnIndex])

	assertions.Equal(codec.Int8, worker.columnDataTypes[0][stringColumnIndex])

}

func TestWorkerDecodeTimeTicks(t *testing.T) {

	worker := workers[0]

	encodedTicks := make([]byte, 4)

	codec.WriteINT32Value(10, 0, encodedTicks)

	decoder := codec.NewDecoder(utils.NewMemoryPool(2, 1, false, utils.DefaultBlobPools))

	rleEncodedBytesLength := make([]byte, 2)

	rleEncodedBytesLength[0] = byte(10)

	rleEncodedBytesLength[1] = byte(10 >> 8)

	qualifiedTickPoolIndex, ticks, err := worker.decodeTimeTicks(append(encodedTicks, append([]byte{0}, append(encodedTicks, rleEncodedBytesLength...)...)...), decoder)

	assertions := assert.New(t)

	assertions.Equal(utils.NotAvailable, qualifiedTickPoolIndex)

	assertions.Nil(ticks)

	assertions.NotNil(err)

	codec.WriteINT32Value(11, 0, encodedTicks)

	qualifiedTickPoolIndex, ticks, err = worker.decodeTimeTicks(append(encodedTicks, make([]byte, 1)...), decoder)

	assertions.Equal(utils.NotAvailable, qualifiedTickPoolIndex)

	assertions.Nil(ticks)

	assertions.NotNil(err)

}

func TestSetColumnContext(t *testing.T) {

	worker := workers[0]

	worker.memoryPoolPositions[0] = 0

	index := len(worker.columns[0])

	worker.columnDataTypes[0][index] = codec.Int8

	columnIndex := worker.setColumnContext(0, "int32.column", codec.Int32, true)

	assertions := assert.New(t)

	assertions.Equal(index, columnIndex)

	assertions.NotEqual(utils.NotAvailable, worker.columnPoolIndices[0][index])

	assertions.Equal(codec.Int32, worker.columnDataTypes[0][index])

	assertions.Contains(worker.columns[0], "int32.column")

	worker.columnDataTypes[0][index] = codec.Int8

	worker.memoryPools[0].ReleaseINT32Pool(worker.columnPoolIndices[0][index])

	worker.columnPoolIndices[0][index] = utils.NotAvailable

	delete(worker.columns[0], "int32.column")

	columnIndex = worker.setColumnContext(0, "int16.column", codec.Int16, true)

	assertions.Equal(index, columnIndex)

	assertions.NotEqual(utils.NotAvailable, worker.columnPoolIndices[0][index])

	assertions.Equal(codec.Int16, worker.columnDataTypes[0][index])

	assertions.Contains(worker.columns[0], "int16.column")

	worker.columnDataTypes[0][index] = codec.Int8

	worker.memoryPools[0].ReleaseINT16Pool(worker.columnPoolIndices[0][index])

	worker.columnPoolIndices[0][index] = utils.NotAvailable

	delete(worker.columns[0], "int16.column")

	index = len(worker.columns[0])

	worker.columns[0]["int64.column"] = index

	worker.columnDataTypes[0][index] = codec.Int64

	int64PoolIndex, int64Values := worker.memoryPools[0].AcquireINT64Pool(4)

	int64Values[0] = 1

	int64Values[1] = 2

	int64Values[2] = 3

	int64Values[3] = 4

	worker.columnPoolIndices[0][index] = int64PoolIndex

	columnIndex = worker.setColumnContext(0, "int64.column", codec.String, false)

	assertions.Equal(index, columnIndex)

	assertions.NotEqual(utils.NotAvailable, worker.columnPoolIndices[0][index])

	assertions.Equal(codec.String, worker.columnDataTypes[0][index])

	assertions.Equal([]string{"1", "2", "3", "4"}, worker.memoryPools[0].GetStringPool(worker.columnPoolIndices[0][index]))

	worker.columnDataTypes[0][index] = codec.Int8

	worker.memoryPools[0].ReleaseStringPool(worker.columnPoolIndices[0][index])

	worker.columnPoolIndices[0][index] = utils.NotAvailable

	delete(worker.columns[0], "int64.column")
}

func TestEvaluateHorizontalHistogramMeanFuncFLOATWithoutGrouping(t *testing.T) {

	worker := workers[0]

	worker.memoryPoolPositions[0] = 0

	sumColumnIndex := len(worker.columns[0])

	worker.columns[0]["float64.sum.column"] = sumColumnIndex

	countColumnIndex := len(worker.columns[0])

	worker.columns[0]["int64.count.column"] = countColumnIndex

	event := worker.WorkerEvents[0]

	event.currentIndex = sumColumnIndex

	event.countIndex = countColumnIndex

	timeObj := time.Now().UTC()

	worker.startTick = utils.UnixToSeconds(time.Date(timeObj.Year(), timeObj.Month(), timeObj.Day(), 0, 0, 0, 0, time.UTC).Unix())

	worker.endTick = utils.UnixToSeconds(time.Date(timeObj.Year(), timeObj.Month(), timeObj.Day(), 23, 59, 59, 59, time.UTC).Unix())

	worker.tick = utils.UnixToSeconds(time.Date(timeObj.Year(), timeObj.Month(), timeObj.Day(), 0, 0, 0, 0, time.UTC).Unix())

	worker.granularity = 300

	worker.groupElementSize = 0

	worker.columnDataTypes[0][sumColumnIndex] = codec.Float64

	worker.columnDataTypes[0][countColumnIndex] = codec.Int64

	var sumValues []float64

	var countValues []int64

	worker.columnPoolIndices[0][sumColumnIndex], sumValues = worker.memoryPools[0].AcquireFLOAT64Pool(5)

	worker.columnPoolIndices[0][countColumnIndex], countValues = worker.memoryPools[0].AcquireINT64Pool(5)

	sumValues[0] = 1

	countValues[0] = 1

	worker.evaluateHorizontalMeanHistogramFuncFLOAT(1, 1, 0, 0)

	assertions := assert.New(t)

	assertions.Equal(float64(2), sumValues[0])

	assertions.Equal(int64(2), countValues[0])

	worker.tick += 300

	sumValues[1] = utils.DummyFLOAT64Value

	countValues[1] = utils.DummyINT64Value

	worker.evaluateHorizontalMeanHistogramFuncFLOAT(1, 1, 0, 0)

	assertions.Equal(float64(1), sumValues[1])

	assertions.Equal(int64(1), countValues[1])

	worker.memoryPools[0].ReleaseFLOAT64Pool(worker.columnPoolIndices[0][sumColumnIndex])

	worker.memoryPools[0].ReleaseINT64Pool(worker.columnPoolIndices[0][countColumnIndex])

	delete(worker.columns[0], "float64.sum.column")

	delete(worker.columns[0], "int64.count.column")

	worker.columnPoolIndices[0][sumColumnIndex] = utils.NotAvailable

	worker.columnPoolIndices[0][countColumnIndex] = utils.NotAvailable

	worker.columnDataTypes[0][sumColumnIndex] = codec.Int8

	worker.columnDataTypes[0][countColumnIndex] = codec.Int8
}

func TestEvaluateHorizontalHistogramMeanFuncFLOATWithGrouping(t *testing.T) {

	worker := workers[0]

	worker.memoryPoolPositions[0] = 0

	sumColumnIndex := len(worker.columns[0])

	worker.columns[0]["float64.sum.column"] = sumColumnIndex

	countColumnIndex := len(worker.columns[0])

	worker.columns[0]["int64.count.column"] = countColumnIndex

	event := worker.WorkerEvents[0]

	event.currentIndex = sumColumnIndex

	event.countIndex = countColumnIndex

	timeObj := time.Now().UTC()

	worker.startTick = utils.UnixToSeconds(time.Date(timeObj.Year(), timeObj.Month(), timeObj.Day(), 0, 0, 0, 0, time.UTC).Unix())

	worker.endTick = utils.UnixToSeconds(time.Date(timeObj.Year(), timeObj.Month(), timeObj.Day(), 23, 59, 59, 59, time.UTC).Unix())

	worker.tick = utils.UnixToSeconds(time.Date(timeObj.Year(), timeObj.Month(), timeObj.Day(), 0, 0, 0, 0, time.UTC).Unix())

	worker.granularity = 300

	worker.groupElementSize = 1

	event.group = "group1"

	worker.externalGrouping = false

	worker.columnDataTypes[0][sumColumnIndex] = codec.Float64

	worker.columnDataTypes[0][countColumnIndex] = codec.Int64

	var sumValues []float64

	var countValues []int64

	worker.columnPoolIndices[0][sumColumnIndex], sumValues = worker.memoryPools[0].AcquireFLOAT64Pool(5)

	worker.columnPoolIndices[0][countColumnIndex], countValues = worker.memoryPools[0].AcquireINT64Pool(5)

	worker.columnGroups[0] = intmap.New[uint64, int](100)

	sumValues[0] = 1

	countValues[0] = 1

	worker.evaluateHorizontalMeanHistogramFuncFLOAT(1, 1, 0, 0)

	assertions := assert.New(t)

	assertions.Equal(float64(2), sumValues[0])

	assertions.Equal(int64(2), countValues[0])

	worker.tick += 300

	sumValues[1] = utils.DummyFLOAT64Value

	countValues[1] = utils.DummyINT64Value

	worker.evaluateHorizontalMeanHistogramFuncFLOAT(1, 1, 0, 0)

	assertions.Equal(float64(1), sumValues[1])

	assertions.Equal(int64(1), countValues[1])

	worker.memoryPools[0].ReleaseFLOAT64Pool(worker.columnPoolIndices[0][sumColumnIndex])

	worker.memoryPools[0].ReleaseINT64Pool(worker.columnPoolIndices[0][countColumnIndex])

	delete(worker.columns[0], "float64.sum.column")

	delete(worker.columns[0], "int64.count.column")

	worker.columnPoolIndices[0][sumColumnIndex] = utils.NotAvailable

	worker.columnPoolIndices[0][countColumnIndex] = utils.NotAvailable

	worker.columnDataTypes[0][sumColumnIndex] = codec.Int8

	worker.columnDataTypes[0][countColumnIndex] = codec.Int8

	worker.columnGroups[0] = nil
}

func TestSetVerticalHistoricalValueColumnFLOAT64(t *testing.T) {

	worker := workers[0]

	columnIndex := len(worker.columns[0])

	column := "float64.column" + utils.ValueSuffix

	worker.columns[0][column] = columnIndex

	worker.memoryPoolPositions[0] = 0

	worker.columnPoolIndices[0][columnIndex], _ = worker.memoryPools[0].AcquireFLOAT64Pool(5)

	worker.columnDataTypes[0][columnIndex] = codec.Float64

	ticks := make([]int32, 6)

	for i := range ticks {

		if i == 0 {

			ticks[0] = utils.UnixToSeconds(time.Now().Unix())

		} else {

			ticks[i] = ticks[i-1] + 1

		}
		worker.positions[int32(i)] = []int{i}

	}

	worker.setVerticalHistoricalValueColumnFLOAT64(0, 0, "float64.column", []float64{1, 2, 3, 4, 5, 6})

	assertions := assert.New(t)

	for i := 0; i < 6; i++ {

		assertions.Equal(float64(i+1), worker.memoryPools[0].GetFLOAT64Pool(worker.columnPoolIndices[0][columnIndex])[i])
	}

	worker.columnDataTypes[0][columnIndex] = codec.Int8

	worker.memoryPools[0].ReleaseFLOAT64Pool(worker.columnPoolIndices[0][columnIndex])

	worker.columnPoolIndices[0][columnIndex] = utils.NotAvailable

	delete(worker.columns[0], column)
}

func TestSetVerticalHistoricalValueColumnINT8Version2(t *testing.T) {

	worker := workers[0]

	columnIndex := len(worker.columns[0])

	column := "int8.column" + utils.ValueSuffix

	worker.columns[0][column] = columnIndex

	worker.memoryPoolPositions[0] = 0

	worker.columnPoolIndices[0][columnIndex], _ = worker.memoryPools[0].AcquireINT64Pool(5)

	worker.columnDataTypes[0][columnIndex] = codec.Int64

	ticks := make([]int32, 6)

	for i := range ticks {

		if i == 0 {

			ticks[0] = utils.UnixToSeconds(time.Now().Unix())

		} else {

			ticks[i] = ticks[i-1] + 1

		}
		worker.positions[int32(i)] = []int{i}

	}

	worker.setVerticalHistoricalValueColumnINT8(0, 0, "int8.column", []int8{1, 2, 3, 4, 5, 6})

	assertions := assert.New(t)

	for i := 0; i < 6; i++ {

		assertions.Equal(int64(i+1), worker.memoryPools[0].GetINT64Pool(worker.columnPoolIndices[0][columnIndex])[i])
	}

	worker.columnDataTypes[0][columnIndex] = codec.Int8

	worker.memoryPools[0].ReleaseINT64Pool(worker.columnPoolIndices[0][columnIndex])

	worker.columnPoolIndices[0][columnIndex] = utils.NotAvailable

	delete(worker.columns[0], column)
}

func TestSetVerticalHistoricalValueColumnINT16Version2(t *testing.T) {

	worker := workers[0]

	columnIndex := len(worker.columns[0])

	column := "int16.column" + utils.ValueSuffix

	worker.columns[0][column] = columnIndex

	worker.memoryPoolPositions[0] = 0

	worker.columnPoolIndices[0][columnIndex], _ = worker.memoryPools[0].AcquireINT64Pool(5)

	worker.columnDataTypes[0][columnIndex] = codec.Int64

	ticks := make([]int32, 6)

	for i := range ticks {

		if i == 0 {

			ticks[0] = utils.UnixToSeconds(time.Now().Unix())

		} else {

			ticks[i] = ticks[i-1] + 1

		}
		worker.positions[int32(i)] = []int{i}

	}

	worker.setVerticalHistoricalValueColumnINT16(0, 0, "int16.column", []int16{1, 2, 3, 4, 5, 6})

	assertions := assert.New(t)

	for i := 0; i < 6; i++ {

		assertions.Equal(int64(i+1), worker.memoryPools[0].GetINT64Pool(worker.columnPoolIndices[0][columnIndex])[i])
	}

	worker.columnDataTypes[0][columnIndex] = codec.Int8

	worker.memoryPools[0].ReleaseINT64Pool(worker.columnPoolIndices[0][columnIndex])

	worker.columnPoolIndices[0][columnIndex] = utils.NotAvailable

	delete(worker.columns[0], column)
}

func TestSetVerticalHistoricalValueColumnINT32Version2(t *testing.T) {

	worker := workers[0]

	columnIndex := len(worker.columns[0])

	column := "int32.column" + utils.ValueSuffix

	worker.columns[0][column] = columnIndex

	worker.memoryPoolPositions[0] = 0

	worker.columnPoolIndices[0][columnIndex], _ = worker.memoryPools[0].AcquireINT64Pool(5)

	worker.columnDataTypes[0][columnIndex] = codec.Int64

	ticks := make([]int32, 6)

	for i := range ticks {

		if i == 0 {

			ticks[0] = utils.UnixToSeconds(time.Now().Unix())

		} else {

			ticks[i] = ticks[i-1] + 1

		}
		worker.positions[int32(i)] = []int{i}
	}

	worker.setVerticalHistoricalValueColumnINT32(0, 0, "int32.column", []int32{1, 2, 3, 4, 5, 6})

	assertions := assert.New(t)

	for i := 0; i < 6; i++ {

		assertions.Equal(int64(i+1), worker.memoryPools[0].GetINT64Pool(worker.columnPoolIndices[0][columnIndex])[i])
	}

	worker.columnDataTypes[0][columnIndex] = codec.Int8

	worker.memoryPools[0].ReleaseINT64Pool(worker.columnPoolIndices[0][columnIndex])

	worker.columnPoolIndices[0][columnIndex] = utils.NotAvailable

	delete(worker.columns[0], column)
}

func TestSetVerticalHistoricalValueColumnINT64Version2(t *testing.T) {

	worker := workers[0]

	columnIndex := len(worker.columns[0])

	column := "int64.column" + utils.ValueSuffix

	worker.columns[0][column] = columnIndex

	worker.memoryPoolPositions[0] = 0

	worker.columnPoolIndices[0][columnIndex], _ = worker.memoryPools[0].AcquireINT64Pool(5)

	worker.columnDataTypes[0][columnIndex] = codec.Int64

	ticks := make([]int32, 6)

	for i := range ticks {

		if i == 0 {

			ticks[0] = utils.UnixToSeconds(time.Now().Unix())

		} else {

			ticks[i] = ticks[i-1] + 1

		}
		worker.positions[int32(i)] = []int{i}

	}

	worker.setVerticalHistoricalValueColumnINT64(0, 0, "int64.column", []int64{1, 2, 3, 4, 5, 6})

	assertions := assert.New(t)

	for i := 0; i < 6; i++ {

		assertions.Equal(int64(i+1), worker.memoryPools[0].GetINT64Pool(worker.columnPoolIndices[0][columnIndex])[i])
	}

	worker.columnDataTypes[0][columnIndex] = codec.Int8

	worker.memoryPools[0].ReleaseINT64Pool(worker.columnPoolIndices[0][columnIndex])

	worker.columnPoolIndices[0][columnIndex] = utils.NotAvailable

	delete(worker.columns[0], column)
}

func TestSetVerticalHistoricalTickColumn(t *testing.T) {

	worker := workers[0]

	columnIndex := len(worker.columns[0])

	column := utils.TimestampKey

	worker.columns[0][column] = columnIndex

	worker.memoryPoolPositions[0] = 0

	worker.columnPoolIndices[0][columnIndex], _ = worker.memoryPools[0].AcquireINT64Pool(5)

	worker.columnDataTypes[0][columnIndex] = codec.Int64

	tick := utils.UnixToSeconds(time.Now().Unix())

	ticks := make([]int32, 6)

	for i := range ticks {

		if i == 0 {

			ticks[0] = utils.UnixToSeconds(time.Now().Unix())

		} else {

			ticks[i] = ticks[i-1] + 1

		}
		worker.positions[int32(i)] = []int{i}

	}

	worker.setVerticalHistoricalTickColumn(0, 0, []int32{tick, tick + 1, tick + 2, tick + 3, tick + 4, tick + 5})

	assertions := assert.New(t)

	for i := 0; i < 6; i++ {

		assertions.Equal(utils.SecondsToUnixMillis(int32(i)+tick), worker.memoryPools[0].GetINT64Pool(worker.columnPoolIndices[0][columnIndex])[i])
	}

	worker.columnDataTypes[0][columnIndex] = codec.Int8

	worker.memoryPools[0].ReleaseINT64Pool(worker.columnPoolIndices[0][columnIndex])

	worker.columnPoolIndices[0][columnIndex] = utils.NotAvailable

	delete(worker.columns[0], column)

	writer.PopulateBenchMarkConfigs(false)
}

// -------------------------------------- SLO testcases -----------------------------------------

func TestDrillDownSLOGlobalBurnRateLast2Days(t *testing.T) {

	defer cache.Clear()

	initDrillDownQueryEngine()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "drilldown-slo-global-burn-rate-last2days.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickhouseQuery := "SELECT cycle_id, burn_rate, (timestamp)*1000 as Timestamp FROM sloGlobalMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 10"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickhouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTableData := make(map[string]map[int64]float64)

	for rows.Next() {

		var burnRate float64

		var cycleId string

		var timestamp int64

		err = rows.Scan(&cycleId, &burnRate, &timestamp)

		if _, ok := clickHouseTableData[cycleId]; !ok {

			clickHouseTableData[cycleId] = make(map[int64]float64)

		}

		clickHouseTableData[cycleId][timestamp] = burnRate

	}

	for index, value := range motadataDBTable["slo~burn.rate^value"] {

		assertions.Equal(utils.ToFixed(clickHouseTableData[motadataDBTable["slo"][index].(string)][motadataDBTable["Timestamp"][index].(int64)]), value)

	}

}

func TestDrillDownSLOGlobalBurnRateLast2DaysWithCycleIdFilter(t *testing.T) {

	defer cache.Clear()

	initDrillDownQueryEngine()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "drilldown-slo-global-burn-rate-last2days-with-cycleid-filter.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickhouseQuery := "SELECT burn_rate, (timestamp)*1000 as Timestamp FROM sloGlobalMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 10 AND cycle_id = '12312'"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickhouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTableData := make(map[int64]float64)

	for rows.Next() {

		var burnRate float64

		var timestamp int64

		err = rows.Scan(&burnRate, &timestamp)

		clickHouseTableData[timestamp] = burnRate

	}

	for index, value := range motadataDBTable["slo~burn.rate^value"] {

		assertions.Equal(utils.ToFixed(clickHouseTableData[motadataDBTable["Timestamp"][index].(int64)]), value)

	}

}

func TestDrillDownSLOGlobalErrorBudgetLeftLast2Days(t *testing.T) {

	defer cache.Clear()

	initDrillDownQueryEngine()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "drilldown-slo-global-error-budget-left-last2days.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickhouseQuery := "SELECT cycle_id, error_budget_left, (timestamp)*1000 as Timestamp FROM sloGlobalMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 11"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickhouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTableData := make(map[string]map[int64]float64)

	for rows.Next() {

		var errorBudgetLeft float64

		var cycleId string

		var timestamp int64

		err = rows.Scan(&cycleId, &errorBudgetLeft, &timestamp)

		if _, ok := clickHouseTableData[cycleId]; !ok {

			clickHouseTableData[cycleId] = make(map[int64]float64)

		}

		clickHouseTableData[cycleId][timestamp] = errorBudgetLeft

	}

	for index, value := range motadataDBTable["slo~error.budget.left^value"] {

		assertions.Equal(utils.ToFixed(clickHouseTableData[motadataDBTable["slo"][index].(string)][motadataDBTable["Timestamp"][index].(int64)]), value)

	}

}

func TestDrillDownSLOGlobalErrorBudgetLeftLast2DaysWithCycleIdFilter(t *testing.T) {

	defer cache.Clear()

	initDrillDownQueryEngine()

	connection, ctx, _ := writer.GetClickhouseDBConnection(writer.TestDatabase1)

	defer utils.CloseClickHouseDBConnection(connection, ctx)

	timeline := utils.GetTimeline(Last2Days)

	assertions := assert.New(t)

	queryContext := utils.MotadataMap{}

	bytes, _ := os.ReadFile(testDir + "drilldown-slo-global-error-budget-left-last2days-with-cycleid-filter.json")

	queryContext = utils.UnmarshalJson(bytes, queryContext)

	queryContext[VisualizationTimeline] = timeline

	clickhouseQuery := "SELECT error_budget_left, (timestamp)*1000 as Timestamp FROM sloGlobalMetrics where timestamp >= " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(FromDateTime)).Unix()) + " and timestamp < " + codec.INT64ToStringValue(time.UnixMilli(timeline.GetInt64Value(ToDateTime)).Unix()) + " AND slo_id = 11 AND cycle_id = '231212'"

	motadataDBTable, errs := notifyExecutor(queryContext)

	assertions.Equal(utils.Empty, errs)

	assertions.NotNil(motadataDBTable)

	rows, err := connection.Query(ctx, clickhouseQuery)

	if err != nil {

		panic(err)
	}

	clickHouseTableData := make(map[int64]float64)

	for rows.Next() {

		var burnRate float64

		var timestamp int64

		err = rows.Scan(&burnRate, &timestamp)

		clickHouseTableData[timestamp] = burnRate

	}

	for index, value := range motadataDBTable["slo~error.budget.left^value"] {

		assertions.Equal(utils.ToFixed(clickHouseTableData[motadataDBTable["Timestamp"][index].(int64)]), value)

	}

}
